import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import {
  workOrders,
  workOrderSalesOrders,
  workOrderStaff,
} from '../drizzle/schema/work-orders.schema';
import { users } from '../drizzle/schema/users.schema';
import { staffMembers } from '../drizzle/schema/staff.schema';
import { salesOrders } from '../drizzle/schema/sales-orders.schema';
import { tasks, TaskReferenceType } from '../drizzle/schema/tasks.schema';
import {
  eq,
  and,
  isNull,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  inArray,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { ActivityType, EntityType } from '../shared/types';
import { TasksService } from '../tasks/tasks.service';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { CreateWorkOrderDto } from './dto/create-work-order.dto';
import { UpdateWorkOrderDto } from './dto/update-work-order.dto';
import { WorkOrderDto } from './dto/work-order.dto';
import { WorkOrderSlimDto } from './dto/work-order-slim.dto';
import { WorkOrderListDto } from './dto/work-order-list.dto';

@Injectable()
export class WorkOrdersService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly tasksService: TasksService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createWorkOrderDto: CreateWorkOrderDto,
    activityMetadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a work order with the same number already exists for this business
      const existingWorkOrder = await this.db
        .select()
        .from(workOrders)
        .where(
          and(
            eq(workOrders.businessId, businessId),
            ilike(
              workOrders.workOrderNumber,
              createWorkOrderDto.workOrderNumber,
            ),
            isNull(workOrders.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (existingWorkOrder) {
        throw new ConflictException(
          `A work order with the number '${createWorkOrderDto.workOrderNumber}' already exists for this business`,
        );
      }

      return await this.db.transaction(async (tx) => {
        // Create the work order
        const [newWorkOrder] = await tx
          .insert(workOrders)
          .values({
            businessId,
            workOrderNumber: createWorkOrderDto.workOrderNumber,
            productId: createWorkOrderDto.productId,
            variantId: createWorkOrderDto.variantId,
            bomId: createWorkOrderDto.bomId,
            quantityToProduce: createWorkOrderDto.quantityToProduce,
            quantityProduced: createWorkOrderDto.quantityProduced || '0.000',
            quantityScrapped: createWorkOrderDto.quantityScrapped || '0.000',
            statusId: createWorkOrderDto.statusId,
            priorityId: createWorkOrderDto.priorityId,
            plannedStartDate: createWorkOrderDto.plannedStartDate,
            plannedEndDate: createWorkOrderDto.plannedEndDate,
            notes: createWorkOrderDto.notes,
            qualityInspectionRequired:
              createWorkOrderDto.qualityInspectionRequired || false,
            qualityInspectionNotes: createWorkOrderDto.qualityInspectionNotes,
            inspectedBy: createWorkOrderDto.inspectedBy,
            createdBy: userId,
            updatedBy: userId,
          })
          .returning({ id: workOrders.id });

        // Handle sales order associations
        if (
          createWorkOrderDto.salesOrderIds &&
          createWorkOrderDto.salesOrderIds.length > 0
        ) {
          const salesOrderAssociations = createWorkOrderDto.salesOrderIds.map(
            (salesOrderId) => ({
              workOrderId: newWorkOrder.id,
              salesOrderId,
              createdBy: userId,
              updatedBy: userId,
            }),
          );

          await tx.insert(workOrderSalesOrders).values(salesOrderAssociations);
        }

        // Handle task creation if requested
        let createdTaskId: string | undefined;
        if (createWorkOrderDto.createTask && createWorkOrderDto.taskTitle) {
          const taskResult = await this.tasksService.createTaskForWorkOrder(
            userId,
            businessId,
            newWorkOrder.id,
            {
              title: createWorkOrderDto.taskTitle,
              description: createWorkOrderDto.taskDescription,
              dueDate: createWorkOrderDto.taskDueDate,
              priority: createWorkOrderDto.taskPriority,
              assignedTo: createWorkOrderDto.taskAssignedTo,
            },
          );
          createdTaskId = taskResult.id;
        }

        // Handle staff assignments
        if (
          createWorkOrderDto.staffIds &&
          createWorkOrderDto.staffIds.length > 0
        ) {
          const staffAssignments = createWorkOrderDto.staffIds.map(
            (staffId) => ({
              workOrderId: newWorkOrder.id,
              staffId,
              // If a task was created and this staff member is assigned to the task, link them
              taskId:
                createdTaskId && staffId === createWorkOrderDto.taskAssignedTo
                  ? createdTaskId
                  : null,
              createdBy: userId,
              updatedBy: userId,
            }),
          );

          await tx.insert(workOrderStaff).values(staffAssignments);
        }

        // Log the activity
        await this.activityLogService.logCreate(
          newWorkOrder.id,
          EntityType.WORK_ORDER,
          userId,
          businessId,
          {
            reason: `Work order ${createWorkOrderDto.workOrderNumber} created`,
            ipAddress: activityMetadata?.ipAddress,
            userAgent: activityMetadata?.userAgent,
            sessionId: activityMetadata?.sessionId,
          },
        );

        return { id: newWorkOrder.id };
      });
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create work order');
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createWorkOrderDto: CreateWorkOrderDto,
    activityMetadata?: ActivityMetadata,
  ): Promise<{ id: string; message: string }> {
    const result = await this.create(
      userId,
      businessId,
      createWorkOrderDto,
      activityMetadata,
    );
    return {
      id: result.id,
      message: 'Work order created successfully',
    };
  }

  async findOne(id: string): Promise<WorkOrderDto> {
    try {
      const workOrder = await this.db
        .select({
          id: workOrders.id,
          businessId: workOrders.businessId,
          workOrderNumber: workOrders.workOrderNumber,
          productId: workOrders.productId,
          variantId: workOrders.variantId,
          bomId: workOrders.bomId,
          quantityToProduce: workOrders.quantityToProduce,
          quantityProduced: workOrders.quantityProduced,
          quantityScrapped: workOrders.quantityScrapped,
          statusId: workOrders.statusId,
          priorityId: workOrders.priorityId,
          plannedStartDate: workOrders.plannedStartDate,
          plannedEndDate: workOrders.plannedEndDate,
          actualStartDate: workOrders.actualStartDate,
          actualEndDate: workOrders.actualEndDate,
          notes: workOrders.notes,
          qualityInspectionRequired: workOrders.qualityInspectionRequired,
          qualityInspectionCompleted: workOrders.qualityInspectionCompleted,
          qualityInspectionDate: workOrders.qualityInspectionDate,
          qualityInspectionResult: workOrders.qualityInspectionResult,
          qualityInspectionNotes: workOrders.qualityInspectionNotes,
          inspectedBy: workOrders.inspectedBy,
          createdBy: users.name,
          updatedBy: sql<string>`COALESCE(${users.name}, '')`,
          createdAt: workOrders.createdAt,
          updatedAt: workOrders.updatedAt,
        })
        .from(workOrders)
        .leftJoin(users, eq(workOrders.createdBy, users.id))
        .where(and(eq(workOrders.id, id), isNull(workOrders.deletedAt)))
        .then((results) => results[0]);

      if (!workOrder) {
        throw new NotFoundException('Work order not found');
      }

      // Get associated sales orders
      const associatedSalesOrders = await this.db
        .select({
          id: salesOrders.id,
          orderNumber: salesOrders.orderNumber,
        })
        .from(workOrderSalesOrders)
        .leftJoin(
          salesOrders,
          eq(workOrderSalesOrders.salesOrderId, salesOrders.id),
        )
        .where(
          and(
            eq(workOrderSalesOrders.workOrderId, id),
            isNull(workOrderSalesOrders.deletedAt),
          ),
        );

      // Get assigned staff
      const assignedStaff = await this.db
        .select({
          id: staffMembers.id,
          name: sql<string>`CONCAT(${staffMembers.firstName}, ' ', ${staffMembers.lastName})`,
          taskId: workOrderStaff.taskId,
        })
        .from(workOrderStaff)
        .leftJoin(staffMembers, eq(workOrderStaff.staffId, staffMembers.id))
        .where(
          and(
            eq(workOrderStaff.workOrderId, id),
            eq(workOrderStaff.isDeleted, false),
          ),
        );

      // Get associated task (if any)
      const associatedTask = await this.db
        .select({
          id: tasks.id,
          title: tasks.title,
          status: tasks.status,
          priority: tasks.priority,
          assignedTo: tasks.assignedTo,
          dueDate: tasks.dueDate,
        })
        .from(tasks)
        .where(
          and(
            eq(tasks.referenceId, id),
            eq(tasks.referenceType, TaskReferenceType.WORK_ORDER),
            isNull(tasks.deletedAt),
          ),
        )
        .then((results) => results[0]);

      return {
        ...workOrder,
        salesOrders: associatedSalesOrders.map((so) => ({
          id: so.id,
          orderNumber: so.orderNumber || '',
        })),
        staff: assignedStaff.map((staff) => ({
          id: staff.id,
          name: staff.name || '',
          taskId: staff.taskId,
        })),
        task: associatedTask
          ? {
              id: associatedTask.id,
              title: associatedTask.title,
              status: associatedTask.status,
              priority: associatedTask.priority,
              assignedTo: associatedTask.assignedTo,
              dueDate: associatedTask.dueDate,
            }
          : undefined,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to fetch work order');
    }
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateWorkOrderDto: UpdateWorkOrderDto,
    activityMetadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if work order exists and belongs to the business
      const existingWorkOrder = await this.db
        .select()
        .from(workOrders)
        .where(
          and(
            eq(workOrders.id, id),
            eq(workOrders.businessId, businessId),
            isNull(workOrders.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!existingWorkOrder) {
        throw new NotFoundException('Work order not found');
      }

      // Check for work order number conflicts if updating the number
      if (
        updateWorkOrderDto.workOrderNumber &&
        updateWorkOrderDto.workOrderNumber !== existingWorkOrder.workOrderNumber
      ) {
        const conflictingWorkOrder = await this.db
          .select()
          .from(workOrders)
          .where(
            and(
              eq(workOrders.businessId, businessId),
              ilike(
                workOrders.workOrderNumber,
                updateWorkOrderDto.workOrderNumber,
              ),
              isNull(workOrders.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (conflictingWorkOrder) {
          throw new ConflictException(
            `A work order with the number '${updateWorkOrderDto.workOrderNumber}' already exists for this business`,
          );
        }
      }

      return await this.db.transaction(async (tx) => {
        // Update the work order
        const updateData: any = {
          updatedBy: userId,
          updatedAt: new Date(),
        };

        // Only include fields that are provided
        if (updateWorkOrderDto.workOrderNumber !== undefined) {
          updateData.workOrderNumber = updateWorkOrderDto.workOrderNumber;
        }
        if (updateWorkOrderDto.productId !== undefined) {
          updateData.productId = updateWorkOrderDto.productId;
        }
        if (updateWorkOrderDto.variantId !== undefined) {
          updateData.variantId = updateWorkOrderDto.variantId;
        }
        if (updateWorkOrderDto.bomId !== undefined) {
          updateData.bomId = updateWorkOrderDto.bomId;
        }
        if (updateWorkOrderDto.quantityToProduce !== undefined) {
          updateData.quantityToProduce = updateWorkOrderDto.quantityToProduce;
        }
        if (updateWorkOrderDto.quantityProduced !== undefined) {
          updateData.quantityProduced = updateWorkOrderDto.quantityProduced;
        }
        if (updateWorkOrderDto.quantityScrapped !== undefined) {
          updateData.quantityScrapped = updateWorkOrderDto.quantityScrapped;
        }
        if (updateWorkOrderDto.statusId !== undefined) {
          updateData.statusId = updateWorkOrderDto.statusId;
        }
        if (updateWorkOrderDto.priorityId !== undefined) {
          updateData.priorityId = updateWorkOrderDto.priorityId;
        }
        if (updateWorkOrderDto.plannedStartDate !== undefined) {
          updateData.plannedStartDate = updateWorkOrderDto.plannedStartDate;
        }
        if (updateWorkOrderDto.plannedEndDate !== undefined) {
          updateData.plannedEndDate = updateWorkOrderDto.plannedEndDate;
        }
        if (updateWorkOrderDto.notes !== undefined) {
          updateData.notes = updateWorkOrderDto.notes;
        }
        if (updateWorkOrderDto.qualityInspectionRequired !== undefined) {
          updateData.qualityInspectionRequired =
            updateWorkOrderDto.qualityInspectionRequired;
        }
        if (updateWorkOrderDto.qualityInspectionCompleted !== undefined) {
          updateData.qualityInspectionCompleted =
            updateWorkOrderDto.qualityInspectionCompleted;
        }
        if (updateWorkOrderDto.qualityInspectionResult !== undefined) {
          updateData.qualityInspectionResult =
            updateWorkOrderDto.qualityInspectionResult;
        }
        if (updateWorkOrderDto.qualityInspectionNotes !== undefined) {
          updateData.qualityInspectionNotes =
            updateWorkOrderDto.qualityInspectionNotes;
        }
        if (updateWorkOrderDto.inspectedBy !== undefined) {
          updateData.inspectedBy = updateWorkOrderDto.inspectedBy;
        }

        await tx
          .update(workOrders)
          .set(updateData)
          .where(eq(workOrders.id, id));

        // Handle sales order associations update
        if (updateWorkOrderDto.salesOrderIds !== undefined) {
          // Remove existing associations
          await tx
            .update(workOrderSalesOrders)
            .set({ deletedAt: new Date(), updatedBy: userId })
            .where(eq(workOrderSalesOrders.workOrderId, id));

          // Add new associations
          if (updateWorkOrderDto.salesOrderIds.length > 0) {
            const salesOrderAssociations = updateWorkOrderDto.salesOrderIds.map(
              (salesOrderId) => ({
                workOrderId: id,
                salesOrderId,
                createdBy: userId,
                updatedBy: userId,
              }),
            );

            await tx
              .insert(workOrderSalesOrders)
              .values(salesOrderAssociations);
          }
        }

        // Handle task updates if provided
        if (
          updateWorkOrderDto.taskTitle ||
          updateWorkOrderDto.taskDescription ||
          updateWorkOrderDto.taskDueDate ||
          updateWorkOrderDto.taskPriority ||
          updateWorkOrderDto.taskAssignedTo !== undefined
        ) {
          // Find existing task associated with this work order
          const existingTask = await tx
            .select({ id: tasks.id })
            .from(tasks)
            .where(
              and(
                eq(tasks.referenceId, id),
                eq(tasks.referenceType, TaskReferenceType.WORK_ORDER),
                isNull(tasks.deletedAt),
              ),
            )
            .then((results) => results[0]);

          if (existingTask) {
            // Update existing task
            const taskUpdateData: any = { updatedBy: userId };
            if (updateWorkOrderDto.taskTitle !== undefined) {
              taskUpdateData.title = updateWorkOrderDto.taskTitle;
            }
            if (updateWorkOrderDto.taskDescription !== undefined) {
              taskUpdateData.description = updateWorkOrderDto.taskDescription;
            }
            if (updateWorkOrderDto.taskDueDate !== undefined) {
              taskUpdateData.dueDate = updateWorkOrderDto.taskDueDate;
            }
            if (updateWorkOrderDto.taskPriority !== undefined) {
              taskUpdateData.priority = updateWorkOrderDto.taskPriority;
            }
            if (updateWorkOrderDto.taskAssignedTo !== undefined) {
              taskUpdateData.assignedTo = updateWorkOrderDto.taskAssignedTo;
            }

            await tx
              .update(tasks)
              .set(taskUpdateData)
              .where(eq(tasks.id, existingTask.id));
          }
        }

        // Handle staff assignments update
        if (updateWorkOrderDto.staffIds !== undefined) {
          // Remove existing assignments
          await tx
            .update(workOrderStaff)
            .set({ isDeleted: true, updatedBy: userId })
            .where(eq(workOrderStaff.workOrderId, id));

          // Add new assignments
          if (updateWorkOrderDto.staffIds.length > 0) {
            // Get the updated task assignment if any
            const updatedTask = await tx
              .select({ id: tasks.id, assignedTo: tasks.assignedTo })
              .from(tasks)
              .where(
                and(
                  eq(tasks.referenceId, id),
                  eq(tasks.referenceType, TaskReferenceType.WORK_ORDER),
                  isNull(tasks.deletedAt),
                ),
              )
              .then((results) => results[0]);

            const staffAssignments = updateWorkOrderDto.staffIds.map(
              (staffId) => ({
                workOrderId: id,
                staffId,
                // Link to task if this staff member is assigned to the task
                taskId:
                  updatedTask && staffId === updatedTask.assignedTo
                    ? updatedTask.id
                    : null,
                createdBy: userId,
                updatedBy: userId,
              }),
            );

            await tx.insert(workOrderStaff).values(staffAssignments);
          }
        }

        // Log the activity
        await this.activityLogService.logUpdate(
          id,
          EntityType.WORK_ORDER,
          userId,
          businessId,
          {
            ipAddress: activityMetadata?.ipAddress,
            userAgent: activityMetadata?.userAgent,
            sessionId: activityMetadata?.sessionId,
          },
        );

        return { id };
      });
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof ConflictException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to update work order');
    }
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateWorkOrderDto: UpdateWorkOrderDto,
    activityMetadata?: ActivityMetadata,
  ): Promise<{ id: string; message: string }> {
    const result = await this.update(
      userId,
      businessId,
      id,
      updateWorkOrderDto,
      activityMetadata,
    );
    return {
      id: result.id,
      message: 'Work order updated successfully',
    };
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    activityMetadata?: ActivityMetadata,
  ): Promise<{ id: string; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if work order exists and belongs to the business
      const existingWorkOrder = await this.db
        .select()
        .from(workOrders)
        .where(
          and(
            eq(workOrders.id, id),
            eq(workOrders.businessId, businessId),
            isNull(workOrders.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!existingWorkOrder) {
        throw new NotFoundException('Work order not found');
      }

      await this.db.transaction(async (tx) => {
        // Soft delete the work order
        await tx
          .update(workOrders)
          .set({
            deletedAt: new Date(),
            deletedBy: userId,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(workOrders.id, id));

        // Soft delete associated sales orders
        await tx
          .update(workOrderSalesOrders)
          .set({ deletedAt: new Date(), updatedBy: userId })
          .where(eq(workOrderSalesOrders.workOrderId, id));

        // Soft delete staff assignments
        await tx
          .update(workOrderStaff)
          .set({ isDeleted: true, updatedBy: userId })
          .where(eq(workOrderStaff.workOrderId, id));

        // Log the activity
        await this.activityLogService.logDelete(
          id,
          EntityType.WORK_ORDER,
          userId,
          businessId,
          {
            reason: `Work order ${existingWorkOrder.workOrderNumber} deleted`,
            ipAddress: activityMetadata?.ipAddress,
            userAgent: activityMetadata?.userAgent,
            sessionId: activityMetadata?.sessionId,
          },
        );
      });

      return {
        id,
        message: 'Work order deleted successfully',
      };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to delete work order');
    }
  }

  async findAllOptimized(
    businessId: string | null,
    page?: number,
    limit?: number,
    from?: string,
    to?: string,
    workOrderNumber?: string,
    statusId?: string,
    priorityId?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: WorkOrderListDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const pageNumber = page || 1;
      const pageSize = limit || 10;
      const offset = (pageNumber - 1) * pageSize;

      // Build where conditions
      const whereConditions = [
        eq(workOrders.businessId, businessId),
        isNull(workOrders.deletedAt),
      ];

      // Add date filters
      if (from) {
        whereConditions.push(gte(workOrders.createdAt, new Date(from)));
      }
      if (to) {
        whereConditions.push(lte(workOrders.createdAt, new Date(to)));
      }

      // Add search filters
      if (workOrderNumber) {
        whereConditions.push(
          ilike(workOrders.workOrderNumber, `%${workOrderNumber}%`),
        );
      }
      if (statusId) {
        whereConditions.push(eq(workOrders.statusId, statusId));
      }
      if (priorityId) {
        whereConditions.push(eq(workOrders.priorityId, priorityId));
      }

      // Handle advanced filters
      if (filters) {
        try {
          const parsedFilters = JSON.parse(filters);
          const filterConditions = parsedFilters.map((filter: any) => {
            switch (filter.operator) {
              case 'iLike':
                return ilike(workOrders[filter.id], `%${filter.value}%`);
              case 'notILike':
                return sql`NOT ${ilike(workOrders[filter.id], `%${filter.value}%`)}`;
              case 'eq':
                return eq(workOrders[filter.id], filter.value);
              case 'ne':
                return sql`${workOrders[filter.id]} != ${filter.value}`;
              case 'isEmpty':
                return sql`${workOrders[filter.id]} IS NULL OR ${workOrders[filter.id]} = ''`;
              case 'isNotEmpty':
                return sql`${workOrders[filter.id]} IS NOT NULL AND ${workOrders[filter.id]} != ''`;
              default:
                return eq(workOrders[filter.id], filter.value);
            }
          });

          if (filterConditions.length > 0) {
            if (joinOperator === 'or') {
              whereConditions.push(or(...filterConditions));
            } else {
              whereConditions.push(...filterConditions);
            }
          }
        } catch {
          // Invalid JSON filters, ignore
        }
      }

      // Handle sorting
      let orderBy = [desc(workOrders.createdAt)];
      if (sort) {
        try {
          const parsedSort = JSON.parse(sort);
          orderBy = parsedSort.map((sortItem: any) => {
            const column = workOrders[sortItem.id];
            return sortItem.desc ? desc(column) : asc(column);
          });
        } catch {
          // Invalid JSON sort, use default
        }
      }

      // Get total count
      const totalResult = await this.db
        .select({ count: sql<number>`count(*)` })
        .from(workOrders)
        .where(and(...whereConditions));

      const total = totalResult[0]?.count || 0;

      // Get paginated data
      const data = await this.db
        .select({
          id: workOrders.id,
          workOrderNumber: workOrders.workOrderNumber,
          productId: workOrders.productId,
          quantityToProduce: workOrders.quantityToProduce,
          quantityProduced: workOrders.quantityProduced,
          statusId: workOrders.statusId,
          priorityId: workOrders.priorityId,
          plannedStartDate: workOrders.plannedStartDate,
          plannedEndDate: workOrders.plannedEndDate,
          createdAt: workOrders.createdAt,
          updatedAt: workOrders.updatedAt,
        })
        .from(workOrders)
        .where(and(...whereConditions))
        .orderBy(...orderBy)
        .limit(pageSize)
        .offset(offset);

      return {
        data,
        total,
        page: pageNumber,
        limit: pageSize,
        totalPages: Math.ceil(total / pageSize),
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException('Failed to fetch work orders');
    }
  }

  async findAllSlim(businessId: string | null): Promise<WorkOrderSlimDto[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const data = await this.db
        .select({
          id: workOrders.id,
          workOrderNumber: workOrders.workOrderNumber,
          productId: workOrders.productId,
          quantityToProduce: workOrders.quantityToProduce,
          quantityProduced: workOrders.quantityProduced,
          statusId: workOrders.statusId,
          priorityId: workOrders.priorityId,
          plannedStartDate: workOrders.plannedStartDate,
          plannedEndDate: workOrders.plannedEndDate,
        })
        .from(workOrders)
        .where(
          and(
            eq(workOrders.businessId, businessId),
            isNull(workOrders.deletedAt),
          ),
        )
        .orderBy(desc(workOrders.createdAt));

      return data;
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException('Failed to fetch work orders');
    }
  }

  async checkWorkOrderNumberAvailability(
    businessId: string | null,
    workOrderNumber: string,
  ): Promise<{ available: boolean }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const existingWorkOrder = await this.db
        .select()
        .from(workOrders)
        .where(
          and(
            eq(workOrders.businessId, businessId),
            ilike(workOrders.workOrderNumber, workOrderNumber),
            isNull(workOrders.deletedAt),
          ),
        )
        .then((results) => results[0]);

      return { available: !existingWorkOrder };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to check work order number availability',
      );
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    workOrdersData: CreateWorkOrderDto[],
    activityMetadata?: ActivityMetadata,
  ): Promise<{ ids: string[]; created: number; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check for duplicate work order numbers within the batch
      const workOrderNumbers = workOrdersData.map((wo) =>
        wo.workOrderNumber.toLowerCase(),
      );
      const duplicates = workOrderNumbers.filter(
        (item, index) => workOrderNumbers.indexOf(item) !== index,
      );

      if (duplicates.length > 0) {
        throw new ConflictException(
          `Duplicate work order numbers found in batch: ${duplicates.join(', ')}`,
        );
      }

      // Check for existing work order numbers in the database
      const existingWorkOrders = await this.db
        .select({ workOrderNumber: workOrders.workOrderNumber })
        .from(workOrders)
        .where(
          and(
            eq(workOrders.businessId, businessId),
            inArray(workOrders.workOrderNumber, workOrderNumbers),
            isNull(workOrders.deletedAt),
          ),
        );

      if (existingWorkOrders.length > 0) {
        const existingNumbers = existingWorkOrders.map(
          (wo) => wo.workOrderNumber,
        );
        throw new ConflictException(
          `Work order numbers already exist: ${existingNumbers.join(', ')}`,
        );
      }

      const createdIds: string[] = [];

      await this.db.transaction(async (tx) => {
        for (const workOrderData of workOrdersData) {
          // Create the work order
          const [newWorkOrder] = await tx
            .insert(workOrders)
            .values({
              businessId,
              workOrderNumber: workOrderData.workOrderNumber,
              productId: workOrderData.productId,
              variantId: workOrderData.variantId,
              bomId: workOrderData.bomId,
              quantityToProduce: workOrderData.quantityToProduce,
              quantityProduced: workOrderData.quantityProduced || '0.000',
              quantityScrapped: workOrderData.quantityScrapped || '0.000',
              statusId: workOrderData.statusId,
              priorityId: workOrderData.priorityId,
              plannedStartDate: workOrderData.plannedStartDate,
              plannedEndDate: workOrderData.plannedEndDate,
              notes: workOrderData.notes,
              qualityInspectionRequired:
                workOrderData.qualityInspectionRequired || false,
              qualityInspectionNotes: workOrderData.qualityInspectionNotes,
              inspectedBy: workOrderData.inspectedBy,
              createdBy: userId,
              updatedBy: userId,
            })
            .returning({ id: workOrders.id });

          createdIds.push(newWorkOrder.id);

          // Handle sales order associations
          if (
            workOrderData.salesOrderIds &&
            workOrderData.salesOrderIds.length > 0
          ) {
            const salesOrderAssociations = workOrderData.salesOrderIds.map(
              (salesOrderId) => ({
                workOrderId: newWorkOrder.id,
                salesOrderId,
                createdBy: userId,
                updatedBy: userId,
              }),
            );

            await tx
              .insert(workOrderSalesOrders)
              .values(salesOrderAssociations);
          }

          // Handle staff assignments
          if (workOrderData.staffIds && workOrderData.staffIds.length > 0) {
            const staffAssignments = workOrderData.staffIds.map((staffId) => ({
              workOrderId: newWorkOrder.id,
              staffId,
              createdBy: userId,
              updatedBy: userId,
            }));

            await tx.insert(workOrderStaff).values(staffAssignments);
          }
        }

        // Log the activity
        await this.activityLogService.logBulkOperation(
          ActivityType.BULK_CREATE,
          EntityType.WORK_ORDER,
          createdIds,
          { count: createdIds.length },
          userId,
          businessId,
          {
            filterCriteria: { workOrdersCount: workOrdersData.length },
            executionStrategy: 'parallel' as any,
            ipAddress: activityMetadata?.ipAddress,
            userAgent: activityMetadata?.userAgent,
            sessionId: activityMetadata?.sessionId,
          },
        );
      });

      return {
        ids: createdIds,
        created: createdIds.length,
        message: `Successfully created ${createdIds.length} work orders`,
      };
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to bulk create work orders');
    }
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    workOrderIds: string[],
    activityMetadata?: ActivityMetadata,
  ): Promise<{
    deleted: number;
    message: string;
    deletedIds: string[];
    failed: string[];
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!workOrderIds || workOrderIds.length === 0) {
        throw new BadRequestException('No work order IDs provided');
      }

      // Check which work orders exist and belong to the business
      const existingWorkOrders = await this.db
        .select({
          id: workOrders.id,
          workOrderNumber: workOrders.workOrderNumber,
        })
        .from(workOrders)
        .where(
          and(
            inArray(workOrders.id, workOrderIds),
            eq(workOrders.businessId, businessId),
            isNull(workOrders.deletedAt),
          ),
        );

      const existingIds = existingWorkOrders.map((wo) => wo.id);
      const failedIds = workOrderIds.filter((id) => !existingIds.includes(id));

      if (existingIds.length === 0) {
        throw new NotFoundException('No valid work orders found to delete');
      }

      await this.db.transaction(async (tx) => {
        // Soft delete the work orders
        await tx
          .update(workOrders)
          .set({
            deletedAt: new Date(),
            deletedBy: userId,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(inArray(workOrders.id, existingIds));

        // Soft delete associated sales orders
        await tx
          .update(workOrderSalesOrders)
          .set({ deletedAt: new Date(), updatedBy: userId })
          .where(inArray(workOrderSalesOrders.workOrderId, existingIds));

        // Soft delete staff assignments
        await tx
          .update(workOrderStaff)
          .set({ isDeleted: true, updatedBy: userId })
          .where(inArray(workOrderStaff.workOrderId, existingIds));

        // Log the activity
        await this.activityLogService.logBulkOperation(
          ActivityType.BULK_DELETE,
          EntityType.WORK_ORDER,
          existingIds,
          { count: existingIds.length },
          userId,
          businessId,
          {
            filterCriteria: { workOrderIdsCount: workOrderIds.length },
            executionStrategy: 'parallel' as any,
            ipAddress: activityMetadata?.ipAddress,
            userAgent: activityMetadata?.userAgent,
            sessionId: activityMetadata?.sessionId,
          },
        );
      });

      return {
        deleted: existingIds.length,
        message: `Successfully deleted ${existingIds.length} work orders`,
        deletedIds: existingIds,
        failed: failedIds,
      };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to bulk delete work orders');
    }
  }

  /**
   * Create a task for a specific work order
   */
  async createTaskForWorkOrder(
    userId: string,
    businessId: string | null,
    workOrderId: string,
    taskData: {
      title: string;
      description?: string;
      dueDate?: string;
      priority?: string;
      assignedTo?: string;
    },
    activityMetadata?: ActivityMetadata,
  ): Promise<{ id: string; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Verify work order exists and belongs to the business
      const workOrder = await this.db
        .select({ id: workOrders.id })
        .from(workOrders)
        .where(
          and(
            eq(workOrders.id, workOrderId),
            eq(workOrders.businessId, businessId),
            isNull(workOrders.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!workOrder) {
        throw new NotFoundException('Work order not found');
      }

      // Check if a task already exists for this work order
      const existingTask = await this.db
        .select({ id: tasks.id })
        .from(tasks)
        .where(
          and(
            eq(tasks.referenceId, workOrderId),
            eq(tasks.referenceType, TaskReferenceType.WORK_ORDER),
            isNull(tasks.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (existingTask) {
        throw new BadRequestException(
          'A task already exists for this work order',
        );
      }

      const result = await this.tasksService.createTaskForWorkOrder(
        userId,
        businessId,
        workOrderId,
        {
          ...taskData,
          priority: taskData.priority as any, // Type assertion for priority
        },
      );

      return {
        id: result.id,
        message: 'Task created successfully for work order',
      };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create task for work order');
    }
  }

  /**
   * Get task associated with a work order
   */
  async getWorkOrderTask(
    userId: string,
    businessId: string | null,
    workOrderId: string,
  ): Promise<any> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Verify work order exists and belongs to the business
      const workOrder = await this.db
        .select({ id: workOrders.id })
        .from(workOrders)
        .where(
          and(
            eq(workOrders.id, workOrderId),
            eq(workOrders.businessId, businessId),
            isNull(workOrders.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!workOrder) {
        throw new NotFoundException('Work order not found');
      }

      // Get associated task
      const task = await this.db
        .select({
          id: tasks.id,
          title: tasks.title,
          description: tasks.description,
          status: tasks.status,
          priority: tasks.priority,
          assignedTo: tasks.assignedTo,
          dueDate: tasks.dueDate,
          createdAt: tasks.createdAt,
          updatedAt: tasks.updatedAt,
        })
        .from(tasks)
        .where(
          and(
            eq(tasks.referenceId, workOrderId),
            eq(tasks.referenceType, TaskReferenceType.WORK_ORDER),
            isNull(tasks.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!task) {
        throw new NotFoundException('No task found for this work order');
      }

      return task;
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to get work order task');
    }
  }

  /**
   * Update task associated with a work order
   */
  async updateWorkOrderTask(
    userId: string,
    businessId: string | null,
    workOrderId: string,
    taskUpdateData: {
      title?: string;
      description?: string;
      status?: string;
      priority?: string;
      assignedTo?: string;
      dueDate?: string;
    },
    activityMetadata?: ActivityMetadata,
  ): Promise<{ id: string; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Verify work order exists and belongs to the business
      const workOrder = await this.db
        .select({ id: workOrders.id })
        .from(workOrders)
        .where(
          and(
            eq(workOrders.id, workOrderId),
            eq(workOrders.businessId, businessId),
            isNull(workOrders.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!workOrder) {
        throw new NotFoundException('Work order not found');
      }

      return await this.db.transaction(async (tx) => {
        // Find existing task
        const existingTask = await tx
          .select({ id: tasks.id, assignedTo: tasks.assignedTo })
          .from(tasks)
          .where(
            and(
              eq(tasks.referenceId, workOrderId),
              eq(tasks.referenceType, TaskReferenceType.WORK_ORDER),
              isNull(tasks.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (!existingTask) {
          throw new NotFoundException('No task found for this work order');
        }

        // Update task
        const updateData: any = { updatedBy: userId };
        if (taskUpdateData.title !== undefined) {
          updateData.title = taskUpdateData.title;
        }
        if (taskUpdateData.description !== undefined) {
          updateData.description = taskUpdateData.description;
        }
        if (taskUpdateData.status !== undefined) {
          updateData.status = taskUpdateData.status;
        }
        if (taskUpdateData.priority !== undefined) {
          updateData.priority = taskUpdateData.priority;
        }
        if (taskUpdateData.assignedTo !== undefined) {
          updateData.assignedTo = taskUpdateData.assignedTo;
        }
        if (taskUpdateData.dueDate !== undefined) {
          updateData.dueDate = taskUpdateData.dueDate;
        }

        await tx
          .update(tasks)
          .set(updateData)
          .where(eq(tasks.id, existingTask.id));

        // Update work order staff assignments if task assignment changed
        if (
          taskUpdateData.assignedTo !== undefined &&
          taskUpdateData.assignedTo !== existingTask.assignedTo
        ) {
          // Remove task link from previous staff assignment
          if (existingTask.assignedTo) {
            await tx
              .update(workOrderStaff)
              .set({ taskId: null, updatedBy: userId })
              .where(
                and(
                  eq(workOrderStaff.workOrderId, workOrderId),
                  eq(workOrderStaff.staffId, existingTask.assignedTo),
                  eq(workOrderStaff.taskId, existingTask.id),
                  eq(workOrderStaff.isDeleted, false),
                ),
              );
          }

          // Add task link to new staff assignment
          if (taskUpdateData.assignedTo) {
            await tx
              .update(workOrderStaff)
              .set({ taskId: existingTask.id, updatedBy: userId })
              .where(
                and(
                  eq(workOrderStaff.workOrderId, workOrderId),
                  eq(workOrderStaff.staffId, taskUpdateData.assignedTo),
                  eq(workOrderStaff.isDeleted, false),
                ),
              );
          }
        }

        // Log the activity
        await this.activityLogService.logUpdate(
          existingTask.id,
          EntityType.TASK,
          userId,
          businessId,
          {
            ipAddress: activityMetadata?.ipAddress,
            userAgent: activityMetadata?.userAgent,
            sessionId: activityMetadata?.sessionId,
          },
        );

        return {
          id: existingTask.id,
          message: 'Task updated successfully',
        };
      });
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to update work order task');
    }
  }

  /**
   * Delete task associated with a work order
   */
  async deleteWorkOrderTask(
    userId: string,
    businessId: string | null,
    workOrderId: string,
    activityMetadata?: ActivityMetadata,
  ): Promise<{ message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Verify work order exists and belongs to the business
      const workOrder = await this.db
        .select({ id: workOrders.id })
        .from(workOrders)
        .where(
          and(
            eq(workOrders.id, workOrderId),
            eq(workOrders.businessId, businessId),
            isNull(workOrders.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!workOrder) {
        throw new NotFoundException('Work order not found');
      }

      return await this.db.transaction(async (tx) => {
        // Find existing task
        const existingTask = await tx
          .select({ id: tasks.id })
          .from(tasks)
          .where(
            and(
              eq(tasks.referenceId, workOrderId),
              eq(tasks.referenceType, TaskReferenceType.WORK_ORDER),
              isNull(tasks.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (!existingTask) {
          throw new NotFoundException('No task found for this work order');
        }

        // Soft delete the task
        await tx
          .update(tasks)
          .set({ deletedAt: new Date(), deletedBy: userId })
          .where(eq(tasks.id, existingTask.id));

        // Remove task links from work order staff assignments
        await tx
          .update(workOrderStaff)
          .set({ taskId: null, updatedBy: userId })
          .where(
            and(
              eq(workOrderStaff.workOrderId, workOrderId),
              eq(workOrderStaff.taskId, existingTask.id),
              eq(workOrderStaff.isDeleted, false),
            ),
          );

        // Log the activity
        await this.activityLogService.logDelete(
          existingTask.id,
          EntityType.TASK,
          userId,
          businessId,
          {
            reason: `Task for work order ${workOrderId} deleted`,
            ipAddress: activityMetadata?.ipAddress,
            userAgent: activityMetadata?.userAgent,
            sessionId: activityMetadata?.sessionId,
          },
        );

        return {
          message: 'Task deleted successfully',
        };
      });
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to delete work order task');
    }
  }
}

import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { WorkOrdersService } from './work-orders.service';
import { CreateWorkOrderDto } from './dto/create-work-order.dto';
import { UpdateWorkOrderDto } from './dto/update-work-order.dto';
import { WorkOrderDto } from './dto/work-order.dto';
import { WorkOrderSlimDto } from './dto/work-order-slim.dto';
import { WorkOrderIdResponseDto } from './dto/work-order-id-response.dto';
import { BulkWorkOrderIdsResponseDto } from './dto/bulk-work-order-ids-response.dto';
import { BulkCreateWorkOrderDto } from './dto/bulk-create-work-order.dto';
import { DeleteWorkOrderResponseDto } from './dto/delete-work-order-response.dto';
import { BulkDeleteWorkOrderDto } from './dto/bulk-delete-work-order.dto';
import { BulkDeleteWorkOrderResponseDto } from './dto/bulk-delete-work-order-response.dto';
import { PaginatedWorkOrdersResponseDto } from './dto/paginated-work-orders-response.dto';
import { WorkOrderNumberAvailabilityResponseDto } from './dto/check-work-order-number.dto';
import { CreateWorkOrderTaskDto } from './dto/create-work-order-task.dto';
import { UpdateWorkOrderTaskDto } from './dto/update-work-order-task.dto';
import { WorkOrderTaskDto } from './dto/work-order-task.dto';
import {
  WorkOrderTaskIdResponseDto,
  WorkOrderTaskDeleteResponseDto,
} from './dto/work-order-task-response.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import { Permission } from '../shared/types/permission.enum';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

@ApiTags('work-orders')
@Controller('work-orders')
@UseGuards(PermissionsGuard)
export class WorkOrdersController {
  constructor(private readonly workOrdersService: WorkOrdersService) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_CREATE)
  @ApiOperation({ summary: 'Create a new work order' })
  @ApiBody({
    description: 'Work order creation data',
    type: CreateWorkOrderDto,
  })
  @ApiResponse({
    status: 201,
    description: 'The work order has been successfully created',
    type: WorkOrderIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Work order number already exists',
  })
  create(
    @Request() req,
    @Body() createWorkOrderDto: CreateWorkOrderDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<WorkOrderIdResponseDto> {
    return this.workOrdersService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createWorkOrderDto,
      metadata,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_CREATE)
  @ApiOperation({ summary: 'Bulk create work orders' })
  @ApiBody({
    description: 'Bulk work order creation data',
    type: BulkCreateWorkOrderDto,
  })
  @ApiResponse({
    status: 201,
    description: 'The work orders have been successfully created',
    type: BulkWorkOrderIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or duplicate work order numbers',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Work order numbers already exist',
  })
  bulkCreate(
    @Request() req,
    @Body() bulkCreateWorkOrderDto: BulkCreateWorkOrderDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkWorkOrderIdsResponseDto> {
    return this.workOrdersService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateWorkOrderDto.workOrders,
      metadata,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_READ)
  @ApiOperation({
    summary: 'Get all work orders for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Filter from date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-05-24',
  })
  @ApiQuery({
    name: 'to',
    description: 'Filter to date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-05-24',
  })
  @ApiQuery({
    name: 'workOrderNumber',
    description: 'Filter by work order number',
    required: false,
    type: String,
    example: 'WO-001',
  })
  @ApiQuery({
    name: 'statusId',
    description: 'Filter by status ID',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'priorityId',
    description: 'Filter by priority ID',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'filters',
    description:
      'Advanced filters as JSON string with operator support. Supported operators: iLike (contains), notILike (does not contain), eq (is), ne (is not), isEmpty (is empty), isNotEmpty (is not empty)',
    required: false,
    type: String,
    example:
      '[{"id":"workOrderNumber","value":"WO-001","operator":"iLike","type":"text","rowId":"1"},{"id":"statusId","value":"active","operator":"eq","type":"select","rowId":"2"}]',
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for advanced filters',
    required: false,
    type: String,
    enum: ['and', 'or'],
    example: 'and',
  })
  @ApiQuery({
    name: 'sort',
    description:
      'Sort configuration as JSON string. Supported fields: workOrderNumber, createdAt, updatedAt',
    required: false,
    type: String,
    example: '[{"id":"createdAt","desc":false}]',
  })
  @ApiResponse({
    status: 200,
    description:
      "Returns all work orders for the user's active business with pagination",
    type: PaginatedWorkOrdersResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('workOrderNumber') workOrderNumber?: string,
    @Query('statusId') statusId?: string,
    @Query('priorityId') priorityId?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedWorkOrdersResponseDto> {
    return this.workOrdersService.findAllOptimized(
      req.user.activeBusinessId,
      page ? parseInt(page.toString()) : undefined,
      limit ? parseInt(limit.toString()) : undefined,
      from,
      to,
      workOrderNumber,
      statusId,
      priorityId,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('check-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_READ)
  @ApiOperation({ summary: 'Check if a work order number is available' })
  @ApiQuery({
    name: 'workOrderNumber',
    description: 'Work order number to check',
    required: true,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns whether the work order number is available',
    type: WorkOrderNumberAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkWorkOrderNumberAvailability(
    @Request() req,
    @Query('workOrderNumber') workOrderNumber: string,
  ): Promise<{ available: boolean }> {
    return this.workOrdersService.checkWorkOrderNumberAvailability(
      req.user.activeBusinessId,
      workOrderNumber,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_READ)
  @ApiOperation({ summary: 'Get all work orders in slim format' })
  @ApiResponse({
    status: 200,
    description: 'All work orders returned successfully',
    type: [WorkOrderSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req): Promise<WorkOrderSlimDto[]> {
    return this.workOrdersService.findAllSlim(req.user.activeBusinessId);
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_READ)
  @ApiOperation({ summary: 'Get a work order by ID' })
  @ApiParam({
    name: 'id',
    description: 'Work order ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the work order with associated data',
    type: WorkOrderDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Work order not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this work order',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findOne(@Param('id') id: string): Promise<WorkOrderDto> {
    return this.workOrdersService.findOne(id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_UPDATE)
  @ApiOperation({ summary: 'Update a work order' })
  @ApiParam({
    name: 'id',
    description: 'Work order ID',
  })
  @ApiBody({
    description: 'Work order update data',
    type: UpdateWorkOrderDto,
  })
  @ApiResponse({
    status: 200,
    description: 'The work order has been successfully updated',
    type: WorkOrderIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 404,
    description: 'Work order not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this work order',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Work order number already exists',
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateWorkOrderDto: UpdateWorkOrderDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<WorkOrderIdResponseDto> {
    return this.workOrdersService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateWorkOrderDto,
      metadata,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_DELETE)
  @ApiOperation({ summary: 'Bulk delete work orders' })
  @ApiBody({
    description: 'Array of work order IDs to delete',
    type: BulkDeleteWorkOrderDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Work orders have been successfully deleted',
    type: BulkDeleteWorkOrderResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or work orders not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'One or more work orders not found',
  })
  async bulkDelete(
    @Request() req,
    @Body() bulkDeleteWorkOrderDto: BulkDeleteWorkOrderDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteWorkOrderResponseDto> {
    return this.workOrdersService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteWorkOrderDto.workOrderIds,
      metadata,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_DELETE)
  @ApiOperation({ summary: 'Delete a work order' })
  @ApiParam({
    name: 'id',
    description: 'Work order ID',
  })
  @ApiResponse({
    status: 200,
    description: 'The work order has been successfully deleted',
    type: DeleteWorkOrderResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Work order not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to delete this work order',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteWorkOrderResponseDto> {
    return this.workOrdersService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }

  @Post(':id/task')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_UPDATE, Permission.TASK_CREATE)
  @ApiOperation({ summary: 'Create a task for a work order' })
  @ApiParam({
    name: 'id',
    description: 'Work order ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiBody({
    description: 'Task creation data',
    type: CreateWorkOrderTaskDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Task has been successfully created for the work order',
    type: WorkOrderTaskIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Work order not found',
  })
  createTaskForWorkOrder(
    @Request() req,
    @Param('id') workOrderId: string,
    @Body() createTaskDto: CreateWorkOrderTaskDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<WorkOrderTaskIdResponseDto> {
    return this.workOrdersService.createTaskForWorkOrder(
      req.user.id,
      req.user.activeBusinessId,
      workOrderId,
      createTaskDto,
      metadata,
    );
  }

  @Get(':id/task')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_READ, Permission.TASK_READ)
  @ApiOperation({ summary: 'Get task associated with a work order' })
  @ApiParam({
    name: 'id',
    description: 'Work order ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: 'Task information retrieved successfully',
    type: WorkOrderTaskDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Work order or task not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  getWorkOrderTask(
    @Request() req,
    @Param('id') workOrderId: string,
  ): Promise<WorkOrderTaskDto> {
    return this.workOrdersService.getWorkOrderTask(
      req.user.id,
      req.user.activeBusinessId,
      workOrderId,
    );
  }

  @Patch(':id/task')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_UPDATE, Permission.TASK_UPDATE)
  @ApiOperation({ summary: 'Update task associated with a work order' })
  @ApiParam({
    name: 'id',
    description: 'Work order ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiBody({
    description: 'Task update data',
    type: UpdateWorkOrderTaskDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Task has been successfully updated',
    type: WorkOrderTaskIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Work order or task not found',
  })
  updateWorkOrderTask(
    @Request() req,
    @Param('id') workOrderId: string,
    @Body() updateTaskDto: UpdateWorkOrderTaskDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<WorkOrderTaskIdResponseDto> {
    return this.workOrdersService.updateWorkOrderTask(
      req.user.id,
      req.user.activeBusinessId,
      workOrderId,
      updateTaskDto,
      metadata,
    );
  }

  @Delete(':id/task')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_UPDATE, Permission.TASK_DELETE)
  @ApiOperation({ summary: 'Delete task associated with a work order' })
  @ApiParam({
    name: 'id',
    description: 'Work order ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: 'Task has been successfully deleted',
    type: WorkOrderTaskDeleteResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Work order or task not found',
  })
  deleteWorkOrderTask(
    @Request() req,
    @Param('id') workOrderId: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<WorkOrderTaskDeleteResponseDto> {
    return this.workOrdersService.deleteWorkOrderTask(
      req.user.id,
      req.user.activeBusinessId,
      workOrderId,
      metadata,
    );
  }
}

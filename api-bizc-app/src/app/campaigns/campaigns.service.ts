import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateCampaignDto } from './dto/create-campaign.dto';
import { UpdateCampaignDto } from './dto/update-campaign.dto';
import { CampaignDto } from './dto/campaign.dto';
import { CampaignSlimDto } from './dto/campaign-slim.dto';
import { campaigns } from '../drizzle/schema/campaigns.schema';
import { and, eq, ilike, gte, lte, desc, asc, count, sql } from 'drizzle-orm';
import { UsersService } from '../users/users.service';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { CampaignStatus } from '../shared/types/campaign.enum';
import { ActivityLogName } from '@app/shared/types/activity.enum';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class CampaignsService {
  constructor(
    @Inject(DRIZZLE) private readonly db: DrizzleDB,
    private readonly usersService: UsersService,
    private readonly activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createCampaignDto: CreateCampaignDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a campaign with the same name already exists for this business
      // Using ilike for case-insensitive comparison
      const existingCampaign = await this.db
        .select()
        .from(campaigns)
        .where(
          and(
            eq(campaigns.businessId, businessId),
            ilike(campaigns.name, createCampaignDto.name),
            eq(campaigns.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingCampaign) {
        throw new ConflictException(
          `Campaign with name "${createCampaignDto.name}" already exists`,
        );
      }

      // Check if code is provided and unique
      if (createCampaignDto.code) {
        const existingCode = await this.db
          .select()
          .from(campaigns)
          .where(
            and(
              eq(campaigns.businessId, businessId),
              ilike(campaigns.code, createCampaignDto.code),
              eq(campaigns.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingCode) {
          throw new ConflictException(
            `Campaign with code "${createCampaignDto.code}" already exists`,
          );
        }
      }

      // Validate date range if both dates are provided
      if (createCampaignDto.startTime && createCampaignDto.endTime) {
        const startDate = new Date(createCampaignDto.startTime);
        const endDate = new Date(createCampaignDto.endTime);

        if (startDate >= endDate) {
          throw new BadRequestException('Start time must be before end time');
        }
      }

      // Insert new campaign
      const [campaign] = await this.db
        .insert(campaigns)
        .values({
          businessId,
          name: createCampaignDto.name,
          code: createCampaignDto.code,
          description: createCampaignDto.description,
          campaignType: createCampaignDto.campaignType,
          status: createCampaignDto.status ?? CampaignStatus.DRAFT,
          templateId: createCampaignDto.templateId,
          startTime: createCampaignDto.startTime
            ? new Date(createCampaignDto.startTime)
            : null,
          endTime: createCampaignDto.endTime
            ? new Date(createCampaignDto.endTime)
            : null,
          targetAudience: createCampaignDto.targetAudience,
          totalContacts: 0,
          successCount: 0,
          failedCount: 0,
          createdBy: userId,
        })
        .returning();

      // Log the campaign creation activity
      await this.activityLogService.log(
        ActivityLogName.CREATE,
        `Campaign "${createCampaignDto.name}" was created`,
        { id: campaign.id.toString(), type: 'campaign' },
        { id: userId, type: 'user' },
        { campaignId: campaign.id, businessId },
      );

      return {
        id: campaign.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create campaign');
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: CampaignDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(campaigns.isDeleted, false),
      eq(campaigns.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(campaigns.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(campaigns.createdAt, toDate));
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(campaigns)
      .where(and(...whereConditions));

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Get campaigns with pagination
    const result = await this.db
      .select()
      .from(campaigns)
      .where(and(...whereConditions))
      .orderBy(desc(campaigns.createdAt))
      .limit(limit)
      .offset(offset);

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.VIEW,
      `Viewed campaigns list (page ${page})`,
      { id: businessId, type: 'business' },
      { id: userId, type: 'user' },
      {},
    );

    const data = await Promise.all(
      result.map((campaign) => this.mapToCampaignDto(campaign)),
    );

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<CampaignDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const campaign = await this.db
      .select()
      .from(campaigns)
      .where(
        and(
          eq(campaigns.id, id),
          eq(campaigns.businessId, businessId),
          eq(campaigns.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!campaign) {
      throw new NotFoundException(`Campaign with ID ${id} not found`);
    }

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.VIEW,
      `Viewed campaign "${campaign.name}"`,
      { id: campaign.id.toString(), type: 'campaign' },
      { id: userId, type: 'user' },
      { campaignId: campaign.id, businessId },
    );

    return this.mapToCampaignDto(campaign);
  }

  async checkNameAvailability(
    userId: string,
    businessId: string | null,
    name: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if a campaign with the same name already exists for this business
    // Using ilike for case-insensitive comparison
    const existingCampaign = await this.db
      .select()
      .from(campaigns)
      .where(
        and(
          eq(campaigns.businessId, businessId),
          ilike(campaigns.name, name),
          eq(campaigns.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingCampaign };
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateCampaignDto: UpdateCampaignDto,
    metadata?: ActivityMetadata,
  ): Promise<CampaignDto> {
    // Get the campaign
    const existingCampaign = await this.db
      .select()
      .from(campaigns)
      .where(and(eq(campaigns.id, id), eq(campaigns.isDeleted, false)))
      .then((results) => results[0]);

    if (!existingCampaign) {
      throw new NotFoundException(`Campaign with ID ${id} not found`);
    }

    // Verify business ownership
    if (businessId !== existingCampaign.businessId) {
      throw new UnauthorizedException('Access denied to update this campaign');
    }

    // Check for name conflicts if name is being updated
    if (
      updateCampaignDto.name &&
      updateCampaignDto.name !== existingCampaign.name
    ) {
      const existingName = await this.db
        .select()
        .from(campaigns)
        .where(
          and(
            eq(campaigns.businessId, businessId),
            ilike(campaigns.name, updateCampaignDto.name),
            eq(campaigns.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingName) {
        throw new ConflictException(
          `Campaign with name "${updateCampaignDto.name}" already exists`,
        );
      }
    }

    // Check for code conflicts if code is being updated
    if (
      updateCampaignDto.code &&
      updateCampaignDto.code !== existingCampaign.code
    ) {
      const existingCode = await this.db
        .select()
        .from(campaigns)
        .where(
          and(
            eq(campaigns.businessId, businessId),
            ilike(campaigns.code, updateCampaignDto.code),
            eq(campaigns.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingCode) {
        throw new ConflictException(
          `Campaign with code "${updateCampaignDto.code}" already exists`,
        );
      }
    }

    // Validate date range if both dates are provided
    const startTime = updateCampaignDto.startTime
      ? new Date(updateCampaignDto.startTime)
      : existingCampaign.startTime;
    const endTime = updateCampaignDto.endTime
      ? new Date(updateCampaignDto.endTime)
      : existingCampaign.endTime;

    if (startTime && endTime && startTime >= endTime) {
      throw new BadRequestException('Start time must be before end time');
    }

    try {
      // Update the campaign
      const [updatedCampaign] = await this.db
        .update(campaigns)
        .set({
          ...updateCampaignDto,
          startTime: updateCampaignDto.startTime
            ? new Date(updateCampaignDto.startTime)
            : undefined,
          endTime: updateCampaignDto.endTime
            ? new Date(updateCampaignDto.endTime)
            : undefined,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(campaigns.id, id))
        .returning();

      // Log the activity
      await this.activityLogService.log(
        ActivityLogName.UPDATE,
        `Campaign "${updatedCampaign.name}" was updated`,
        { id: updatedCampaign.id.toString(), type: 'campaign' },
        { id: userId, type: 'user' },
        { campaignId: updatedCampaign.id, businessId },
      );

      return this.mapToCampaignDto(updatedCampaign);
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to update campaign');
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ message: string; id: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Get the campaign
    const existingCampaign = await this.db
      .select()
      .from(campaigns)
      .where(
        and(
          eq(campaigns.id, id),
          eq(campaigns.businessId, businessId),
          eq(campaigns.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingCampaign) {
      throw new NotFoundException(`Campaign with ID ${id} not found`);
    }

    // Soft delete the campaign
    await this.db
      .update(campaigns)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(campaigns.id, id));

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.DELETE,
      `Campaign "${existingCampaign.name}" was deleted`,
      { id: existingCampaign.id.toString(), type: 'campaign' },
      { id: userId, type: 'user' },
      { campaignId: existingCampaign.id, businessId },
    );

    return {
      message: 'Campaign deleted successfully',
      id: existingCampaign.id,
    };
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<CampaignSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Find all campaigns with only essential fields
    const campaignResults = await this.db
      .select({
        id: campaigns.id,
        name: campaigns.name,
        status: campaigns.status,
      })
      .from(campaigns)
      .where(
        and(
          eq(campaigns.isDeleted, false),
          eq(campaigns.businessId, businessId),
        ),
      )
      .orderBy(desc(campaigns.createdAt));

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.VIEW,
      'Viewed campaign list (slim)',
      { id: businessId, type: 'business' },
      { id: userId, type: 'user' },
      {},
    );

    return campaignResults.map((campaign) => ({
      id: campaign.id.toString(),
      name: campaign.name,
      status: campaign.status,
    }));
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    code?: string,
    status?: string,
    campaignType?: string,
    targetAudience?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: CampaignDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(campaigns.isDeleted, false),
      eq(campaigns.businessId, businessId),
    ];

    // Add search filters
    if (name) {
      whereConditions.push(ilike(campaigns.name, `%${name}%`));
    }

    if (code) {
      whereConditions.push(ilike(campaigns.code, `%${code}%`));
    }

    if (status) {
      whereConditions.push(eq(campaigns.status, status as CampaignStatus));
    }

    if (campaignType) {
      whereConditions.push(eq(campaigns.campaignType, campaignType as any));
    }

    if (targetAudience) {
      whereConditions.push(eq(campaigns.targetAudience, targetAudience as any));
    }

    // Add date range filtering
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(campaigns.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(campaigns.createdAt, toDate));
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(campaigns)
      .where(and(...whereConditions));

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Determine sort order
    let orderBy;
    if (sort) {
      const [field, direction] = sort.split(':');
      const isDesc = direction?.toLowerCase() === 'desc';

      switch (field) {
        case 'name':
          orderBy = isDesc ? desc(campaigns.name) : asc(campaigns.name);
          break;
        case 'status':
          orderBy = isDesc ? desc(campaigns.status) : asc(campaigns.status);
          break;
        case 'campaignType':
          orderBy = isDesc
            ? desc(campaigns.campaignType)
            : asc(campaigns.campaignType);
          break;
        case 'createdAt':
          orderBy = isDesc
            ? desc(campaigns.createdAt)
            : asc(campaigns.createdAt);
          break;
        default:
          orderBy = desc(campaigns.createdAt);
      }
    } else {
      orderBy = desc(campaigns.createdAt);
    }

    // Get campaigns with pagination
    const result = await this.db
      .select()
      .from(campaigns)
      .where(and(...whereConditions))
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.VIEW,
      `Viewed campaigns list (optimized, page ${page})`,
      { id: businessId, type: 'business' },
      { id: userId, type: 'user' },
      {},
    );

    const data = await Promise.all(
      result.map((campaign) => this.mapToCampaignDto(campaign)),
    );

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  private async mapToCampaignDto(
    campaign: typeof campaigns.$inferSelect,
  ): Promise<CampaignDto> {
    // Get user names for createdBy and updatedBy
    const createdByName = await this.usersService.getUserName(
      campaign.createdBy.toString(),
    );
    let updatedByName: string | undefined;
    if (campaign.updatedBy) {
      updatedByName = await this.usersService.getUserName(
        campaign.updatedBy.toString(),
      );
    }

    return {
      id: campaign.id.toString(),
      businessId: campaign.businessId.toString(),
      name: campaign.name,
      code: campaign.code,
      description: campaign.description,
      campaignType: campaign.campaignType,
      status: campaign.status,
      templateId: campaign.templateId?.toString(),
      startTime: campaign.startTime,
      endTime: campaign.endTime,
      targetAudience: campaign.targetAudience,
      totalContacts: campaign.totalContacts,
      successCount: campaign.successCount,
      failedCount: campaign.failedCount,
      createdBy: createdByName,
      updatedBy: updatedByName,
      createdAt: campaign.createdAt,
      updatedAt: campaign.updatedAt,
    };
  }
}

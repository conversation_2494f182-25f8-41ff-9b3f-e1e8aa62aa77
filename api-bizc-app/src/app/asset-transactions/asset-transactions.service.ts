import {
  Injectable,
  Inject,
  NotFoundException,
  BadRequestException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { assetTransactions } from '../drizzle/schema/asset-transactions.schema';
import { assets } from '../drizzle/schema/assets.schema';
import { staffMembers } from '../drizzle/schema/staff.schema';
import { locations } from '../drizzle/schema/locations.schema';
import { users } from '../drizzle/schema/users.schema';
import { CreateAssetTransactionDto } from './dto/create-asset-transaction.dto';
import { UpdateAssetTransactionDto } from './dto/update-asset-transaction.dto';
import { AssetTransactionDto } from './dto/asset-transaction.dto';
import { AssetTransactionListDto } from './dto/asset-transaction-list.dto';
import { AssetTransactionSlimDto } from './dto/asset-transaction-slim.dto';
import {
  and,
  eq,
  isNull,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  inArray,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { MediaService } from '../media/media.service';
import { ActivityLogName } from '../shared/types';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class AssetTransactionsService {
  constructor(
    @Inject(DRIZZLE) private readonly db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly mediaService: MediaService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createAssetTransactionDto: CreateAssetTransactionDto,
    attachmentFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<AssetTransactionDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a transaction with the same reference number already exists for this business
      const existingTransaction = await this.db
        .select()
        .from(assetTransactions)
        .where(
          and(
            eq(assetTransactions.businessId, businessId),
            ilike(assetTransactions.refNo, createAssetTransactionDto.refNo),
            isNull(assetTransactions.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (existingTransaction) {
        throw new ConflictException(
          `An asset transaction with the reference number '${createAssetTransactionDto.refNo}' already exists for this business`,
        );
      }

      // Validate asset exists if provided
      if (createAssetTransactionDto.assetId) {
        const asset = await this.db
          .select()
          .from(assets)
          .where(
            and(
              eq(assets.id, createAssetTransactionDto.assetId),
              eq(assets.businessId, businessId),
              eq(assets.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!asset) {
          throw new NotFoundException(
            `Asset with ID ${createAssetTransactionDto.assetId} not found`,
          );
        }
      }

      // Validate receiver exists if provided
      if (createAssetTransactionDto.receiver) {
        const receiver = await this.db
          .select()
          .from(staffMembers)
          .where(
            and(
              eq(staffMembers.id, createAssetTransactionDto.receiver),
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!receiver) {
          throw new NotFoundException(
            `Staff member with ID ${createAssetTransactionDto.receiver} not found`,
          );
        }
      }

      // Validate locations exist if provided
      if (createAssetTransactionDto.fromLocationId) {
        const fromLocation = await this.db
          .select()
          .from(locations)
          .where(
            and(
              eq(locations.id, createAssetTransactionDto.fromLocationId),
              eq(locations.businessId, businessId),
              eq(locations.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!fromLocation) {
          throw new NotFoundException(
            `From location with ID ${createAssetTransactionDto.fromLocationId} not found`,
          );
        }
      }

      if (createAssetTransactionDto.toLocationId) {
        const toLocation = await this.db
          .select()
          .from(locations)
          .where(
            and(
              eq(locations.id, createAssetTransactionDto.toLocationId),
              eq(locations.businessId, businessId),
              eq(locations.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!toLocation) {
          throw new NotFoundException(
            `To location with ID ${createAssetTransactionDto.toLocationId} not found`,
          );
        }
      }

      // Create the asset transaction
      const [newAssetTransaction] = await this.db
        .insert(assetTransactions)
        .values({
          businessId,
          assetId: createAssetTransactionDto.assetId,
          transactionType: createAssetTransactionDto.transactionType,
          refNo: createAssetTransactionDto.refNo,
          receiver: createAssetTransactionDto.receiver,
          fromLocationId: createAssetTransactionDto.fromLocationId,
          toLocationId: createAssetTransactionDto.toLocationId,
          transactionDatetime: new Date(
            createAssetTransactionDto.transactionDatetime,
          ),
          allocatedUpto: createAssetTransactionDto.allocatedUpto
            ? new Date(createAssetTransactionDto.allocatedUpto)
                .toISOString()
                .split('T')[0]
            : null,
          reason: createAssetTransactionDto.reason,
          parentId: createAssetTransactionDto.parentId,
          createdBy: userId,
        })
        .returning();

      // Handle file attachments if provided
      if (attachmentFiles && attachmentFiles.length > 0) {
        const filesToUpload = createAssetTransactionDto.attachmentIndexes
          ? createAssetTransactionDto.attachmentIndexes
              .map((index) => attachmentFiles[index])
              .filter(Boolean)
          : attachmentFiles;

        if (filesToUpload.length > 0) {
          await this.mediaService.uploadMultipleMediaWithReference(
            filesToUpload,
            'asset-transactions',
            businessId,
            userId,
            newAssetTransaction.id,
          );
        }
      }

      // Log the activity
      await this.activityLogService.log(
        ActivityLogName.CREATE,
        `Asset transaction "${createAssetTransactionDto.refNo}" was created`,
        { id: newAssetTransaction.id.toString(), type: 'asset_transaction' },
        { id: userId, type: 'user' },
        { assetTransactionId: newAssetTransaction.id, businessId },
      );

      return this.findOne(userId, businessId, newAssetTransaction.id);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create asset transaction: ${error.message}`,
      );
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    refNo?: string,
    transactionType?: string,
    assetId?: string,
    receiver?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: AssetTransactionListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const offset = (page - 1) * limit;
      const baseConditions = [
        eq(assetTransactions.businessId, businessId),
        isNull(assetTransactions.deletedAt),
      ];

      // Date range filters
      if (from) {
        baseConditions.push(gte(assetTransactions.createdAt, new Date(from)));
      }
      if (to) {
        baseConditions.push(lte(assetTransactions.createdAt, new Date(to)));
      }

      // Basic filters
      if (refNo) {
        baseConditions.push(ilike(assetTransactions.refNo, `%${refNo}%`));
      }
      if (transactionType) {
        baseConditions.push(
          eq(assetTransactions.transactionType, transactionType as any),
        );
      }
      if (assetId) {
        baseConditions.push(eq(assetTransactions.assetId, assetId));
      }
      if (receiver) {
        baseConditions.push(eq(assetTransactions.receiver, receiver));
      }

      // Advanced filters
      let advancedConditions: any[] = [];
      if (filters) {
        try {
          const filterArray = JSON.parse(filters);
          advancedConditions = this.buildAdvancedFilters(filterArray);
        } catch {
          throw new BadRequestException('Invalid filters format');
        }
      }

      // Combine conditions
      let whereCondition;
      if (advancedConditions.length > 0) {
        const combinedAdvanced =
          joinOperator === 'or'
            ? or(...advancedConditions)
            : and(...advancedConditions);
        whereCondition = and(...baseConditions, combinedAdvanced);
      } else {
        whereCondition = and(...baseConditions);
      }

      // Build sort condition
      let orderBy;
      if (sort) {
        const [field, direction] = sort.split(':');
        const isDesc = direction?.toLowerCase() === 'desc';

        switch (field) {
          case 'refNo':
            orderBy = isDesc
              ? desc(assetTransactions.refNo)
              : asc(assetTransactions.refNo);
            break;
          case 'transactionType':
            orderBy = isDesc
              ? desc(assetTransactions.transactionType)
              : asc(assetTransactions.transactionType);
            break;
          case 'transactionDatetime':
            orderBy = isDesc
              ? desc(assetTransactions.transactionDatetime)
              : asc(assetTransactions.transactionDatetime);
            break;
          case 'createdAt':
            orderBy = isDesc
              ? desc(assetTransactions.createdAt)
              : asc(assetTransactions.createdAt);
            break;
          default:
            orderBy = desc(assetTransactions.createdAt);
        }
      } else {
        orderBy = desc(assetTransactions.createdAt);
      }

      // Get total count
      const totalResult = await this.db
        .select({ count: sql<number>`count(*)` })
        .from(assetTransactions)
        .where(whereCondition);

      const total = Number(totalResult[0]?.count) || 0;
      const totalPages = Math.ceil(total / limit);

      // Get paginated data with joins
      const transactionResults = await this.db
        .select({
          id: assetTransactions.id,
          refNo: assetTransactions.refNo,
          transactionType: assetTransactions.transactionType,
          transactionDatetime: assetTransactions.transactionDatetime,
          allocatedUpto: assetTransactions.allocatedUpto,
          reason: assetTransactions.reason,
          createdAt: assetTransactions.createdAt,
          // Asset info
          assetName: assets.name,
          // Receiver info
          receiverName: sql<string>`CONCAT(${staffMembers.firstName}, ' ', ${staffMembers.lastName})`,
          // Location info
          fromLocationName: sql<string>`from_loc.name`,
          toLocationName: sql<string>`to_loc.name`,
        })
        .from(assetTransactions)
        .leftJoin(assets, eq(assetTransactions.assetId, assets.id))
        .leftJoin(staffMembers, eq(assetTransactions.receiver, staffMembers.id))
        .leftJoin(
          sql`${locations} as from_loc`,
          eq(assetTransactions.fromLocationId, sql`from_loc.id`),
        )
        .leftJoin(
          sql`${locations} as to_loc`,
          eq(assetTransactions.toLocationId, sql`to_loc.id`),
        )
        .where(whereCondition)
        .orderBy(orderBy)
        .limit(limit)
        .offset(offset);

      const data = transactionResults.map((transaction) =>
        this.mapToAssetTransactionListDto(transaction),
      );

      // Log the activity
      await this.activityLogService.log(
        ActivityLogName.VIEW,
        'Viewed asset transactions list',
        { id: businessId, type: 'business' },
        { id: userId, type: 'user' },
        {},
      );

      return {
        data,
        meta: {
          total,
          page,
          totalPages,
        },
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to fetch asset transactions: ${error.message}`,
      );
    }
  }

  async findOne(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<AssetTransactionDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const transactionResult = await this.db
        .select({
          id: assetTransactions.id,
          businessId: assetTransactions.businessId,
          assetId: assetTransactions.assetId,
          transactionType: assetTransactions.transactionType,
          refNo: assetTransactions.refNo,
          receiver: assetTransactions.receiver,
          fromLocationId: assetTransactions.fromLocationId,
          toLocationId: assetTransactions.toLocationId,
          transactionDatetime: assetTransactions.transactionDatetime,
          allocatedUpto: assetTransactions.allocatedUpto,
          reason: assetTransactions.reason,
          parentId: assetTransactions.parentId,
          createdAt: assetTransactions.createdAt,
          updatedAt: assetTransactions.updatedAt,
          // Asset info
          assetName: assets.name,
          // Receiver info
          receiverName: sql<string>`CONCAT(${staffMembers.firstName}, ' ', ${staffMembers.lastName})`,
          // Location info
          fromLocationName: sql<string>`from_loc.name`,
          toLocationName: sql<string>`to_loc.name`,
          // User info
          createdByName: sql<string>`CONCAT(created_user.first_name, ' ', created_user.last_name)`,
          updatedByName: sql<string>`CONCAT(updated_user.first_name, ' ', updated_user.last_name)`,
        })
        .from(assetTransactions)
        .leftJoin(assets, eq(assetTransactions.assetId, assets.id))
        .leftJoin(staffMembers, eq(assetTransactions.receiver, staffMembers.id))
        .leftJoin(
          sql`${locations} as from_loc`,
          eq(assetTransactions.fromLocationId, sql`from_loc.id`),
        )
        .leftJoin(
          sql`${locations} as to_loc`,
          eq(assetTransactions.toLocationId, sql`to_loc.id`),
        )
        .leftJoin(
          sql`${users} as created_user`,
          eq(assetTransactions.createdBy, sql`created_user.id`),
        )
        .leftJoin(
          sql`${users} as updated_user`,
          eq(assetTransactions.updatedBy, sql`updated_user.id`),
        )
        .where(
          and(
            eq(assetTransactions.id, id),
            eq(assetTransactions.businessId, businessId),
            isNull(assetTransactions.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!transactionResult) {
        throw new NotFoundException(
          `Asset transaction with ID ${id} not found`,
        );
      }

      // Fetch attachments for this transaction
      const attachments = await this.mediaService.findByReferenceId(
        transactionResult.id,
        businessId,
      );

      // Log the activity
      await this.activityLogService.log(
        ActivityLogName.VIEW,
        `Viewed asset transaction "${transactionResult.refNo}"`,
        { id: transactionResult.id.toString(), type: 'asset_transaction' },
        { id: userId, type: 'user' },
        { assetTransactionId: transactionResult.id, businessId },
      );

      return this.mapToAssetTransactionDto(transactionResult, attachments);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to fetch asset transaction: ${error.message}`,
      );
    }
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateAssetTransactionDto: UpdateAssetTransactionDto,
    attachmentFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<AssetTransactionDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if the transaction exists
      const existingTransaction = await this.db
        .select()
        .from(assetTransactions)
        .where(
          and(
            eq(assetTransactions.id, id),
            eq(assetTransactions.businessId, businessId),
            isNull(assetTransactions.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!existingTransaction) {
        throw new NotFoundException(
          `Asset transaction with ID ${id} not found`,
        );
      }

      // Check if reference number is being updated and if it conflicts
      if (
        updateAssetTransactionDto.refNo &&
        updateAssetTransactionDto.refNo !== existingTransaction.refNo
      ) {
        const conflictingTransaction = await this.db
          .select()
          .from(assetTransactions)
          .where(
            and(
              eq(assetTransactions.businessId, businessId),
              ilike(assetTransactions.refNo, updateAssetTransactionDto.refNo),
              isNull(assetTransactions.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (conflictingTransaction) {
          throw new ConflictException(
            `An asset transaction with the reference number '${updateAssetTransactionDto.refNo}' already exists for this business`,
          );
        }
      }

      // Validate asset exists if provided
      if (updateAssetTransactionDto.assetId) {
        const asset = await this.db
          .select()
          .from(assets)
          .where(
            and(
              eq(assets.id, updateAssetTransactionDto.assetId),
              eq(assets.businessId, businessId),
              eq(assets.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!asset) {
          throw new NotFoundException(
            `Asset with ID ${updateAssetTransactionDto.assetId} not found`,
          );
        }
      }

      // Validate receiver exists if provided
      if (updateAssetTransactionDto.receiver) {
        const receiver = await this.db
          .select()
          .from(staffMembers)
          .where(
            and(
              eq(staffMembers.id, updateAssetTransactionDto.receiver),
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!receiver) {
          throw new NotFoundException(
            `Staff member with ID ${updateAssetTransactionDto.receiver} not found`,
          );
        }
      }

      // Validate locations exist if provided
      if (updateAssetTransactionDto.fromLocationId) {
        const fromLocation = await this.db
          .select()
          .from(locations)
          .where(
            and(
              eq(locations.id, updateAssetTransactionDto.fromLocationId),
              eq(locations.businessId, businessId),
              eq(locations.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!fromLocation) {
          throw new NotFoundException(
            `From location with ID ${updateAssetTransactionDto.fromLocationId} not found`,
          );
        }
      }

      if (updateAssetTransactionDto.toLocationId) {
        const toLocation = await this.db
          .select()
          .from(locations)
          .where(
            and(
              eq(locations.id, updateAssetTransactionDto.toLocationId),
              eq(locations.businessId, businessId),
              eq(locations.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!toLocation) {
          throw new NotFoundException(
            `To location with ID ${updateAssetTransactionDto.toLocationId} not found`,
          );
        }
      }

      // Prepare update data
      const updateData: any = {
        updatedBy: userId,
        updatedAt: new Date(),
      };

      if (updateAssetTransactionDto.assetId !== undefined) {
        updateData.assetId = updateAssetTransactionDto.assetId;
      }
      if (updateAssetTransactionDto.transactionType !== undefined) {
        updateData.transactionType = updateAssetTransactionDto.transactionType;
      }
      if (updateAssetTransactionDto.refNo !== undefined) {
        updateData.refNo = updateAssetTransactionDto.refNo;
      }
      if (updateAssetTransactionDto.receiver !== undefined) {
        updateData.receiver = updateAssetTransactionDto.receiver;
      }
      if (updateAssetTransactionDto.fromLocationId !== undefined) {
        updateData.fromLocationId = updateAssetTransactionDto.fromLocationId;
      }
      if (updateAssetTransactionDto.toLocationId !== undefined) {
        updateData.toLocationId = updateAssetTransactionDto.toLocationId;
      }
      if (updateAssetTransactionDto.transactionDatetime !== undefined) {
        updateData.transactionDatetime = new Date(
          updateAssetTransactionDto.transactionDatetime,
        );
      }
      if (updateAssetTransactionDto.allocatedUpto !== undefined) {
        updateData.allocatedUpto = updateAssetTransactionDto.allocatedUpto
          ? new Date(updateAssetTransactionDto.allocatedUpto)
              .toISOString()
              .split('T')[0]
          : null;
      }
      if (updateAssetTransactionDto.reason !== undefined) {
        updateData.reason = updateAssetTransactionDto.reason;
      }
      if (updateAssetTransactionDto.parentId !== undefined) {
        updateData.parentId = updateAssetTransactionDto.parentId;
      }

      // Update the transaction
      await this.db
        .update(assetTransactions)
        .set(updateData)
        .where(eq(assetTransactions.id, id));

      // Handle file attachments if provided
      if (attachmentFiles && attachmentFiles.length > 0) {
        const filesToUpload = updateAssetTransactionDto.attachmentIndexes
          ? updateAssetTransactionDto.attachmentIndexes
              .map((index) => attachmentFiles[index])
              .filter(Boolean)
          : attachmentFiles;

        if (filesToUpload.length > 0) {
          await this.mediaService.updateMediaForReference(
            id,
            filesToUpload,
            'asset-transactions',
            businessId,
            userId,
          );
        }
      }

      // Log the activity
      await this.activityLogService.log(
        ActivityLogName.UPDATE,
        `Asset transaction "${existingTransaction.refNo}" was updated`,
        { id: id.toString(), type: 'asset_transaction' },
        { id: userId, type: 'user' },
        { assetTransactionId: id, businessId },
      );

      return this.findOne(userId, businessId, id);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update asset transaction: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if the transaction exists
      const existingTransaction = await this.db
        .select()
        .from(assetTransactions)
        .where(
          and(
            eq(assetTransactions.id, id),
            eq(assetTransactions.businessId, businessId),
            isNull(assetTransactions.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!existingTransaction) {
        throw new NotFoundException(
          `Asset transaction with ID ${id} not found`,
        );
      }

      // Soft delete the transaction
      await this.db
        .update(assetTransactions)
        .set({
          deletedBy: userId,
          deletedAt: new Date(),
        })
        .where(eq(assetTransactions.id, id));

      // Log the activity
      await this.activityLogService.log(
        ActivityLogName.DELETE,
        `Asset transaction "${existingTransaction.refNo}" was deleted`,
        { id: id.toString(), type: 'asset_transaction' },
        { id: userId, type: 'user' },
        { assetTransactionId: id, businessId },
      );

      return {
        success: true,
        message: 'Asset transaction deleted successfully',
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete asset transaction: ${error.message}`,
      );
    }
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    ids: string[],
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; deletedCount: number; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!ids || ids.length === 0) {
        throw new BadRequestException(
          'No transaction IDs provided for deletion',
        );
      }

      // Check which transactions exist
      const existingTransactions = await this.db
        .select({ id: assetTransactions.id, refNo: assetTransactions.refNo })
        .from(assetTransactions)
        .where(
          and(
            inArray(assetTransactions.id, ids),
            eq(assetTransactions.businessId, businessId),
            isNull(assetTransactions.deletedAt),
          ),
        );

      if (existingTransactions.length === 0) {
        throw new NotFoundException(
          'No valid asset transactions found for deletion',
        );
      }

      const existingIds = existingTransactions.map((t) => t.id);

      // Bulk soft delete
      await this.db
        .update(assetTransactions)
        .set({
          deletedBy: userId,
          deletedAt: new Date(),
        })
        .where(inArray(assetTransactions.id, existingIds));

      // Log the activity
      await this.activityLogService.log(
        ActivityLogName.DELETE,
        `Bulk deleted ${existingTransactions.length} asset transactions`,
        { id: businessId, type: 'business' },
        { id: userId, type: 'user' },
        { deletedTransactionIds: existingIds, businessId },
      );

      return {
        success: true,
        deletedCount: existingTransactions.length,
        message: `${existingTransactions.length} asset transactions deleted successfully`,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete asset transactions: ${error.message}`,
      );
    }
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<AssetTransactionSlimDto[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const transactions = await this.db
        .select({
          id: assetTransactions.id,
          refNo: assetTransactions.refNo,
          transactionType: assetTransactions.transactionType,
          transactionDatetime: assetTransactions.transactionDatetime,
          assetName: assets.name,
        })
        .from(assetTransactions)
        .leftJoin(assets, eq(assetTransactions.assetId, assets.id))
        .where(
          and(
            eq(assetTransactions.businessId, businessId),
            isNull(assetTransactions.deletedAt),
          ),
        )
        .orderBy(desc(assetTransactions.createdAt));

      // Log the activity
      await this.activityLogService.log(
        ActivityLogName.VIEW,
        'Viewed asset transactions list (slim)',
        { id: businessId, type: 'business' },
        { id: userId, type: 'user' },
        {},
      );

      return transactions.map((transaction) =>
        this.mapToAssetTransactionSlimDto(transaction),
      );
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to fetch asset transactions: ${error.message}`,
      );
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createAssetTransactionDto: CreateAssetTransactionDto,
    metadata?: ActivityMetadata,
    attachmentFiles?: Express.Multer.File[],
  ): Promise<{ id: string }> {
    const transaction = await this.create(
      userId,
      businessId,
      createAssetTransactionDto,
      attachmentFiles,
      metadata,
    );
    return { id: transaction.id };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateAssetTransactionDto: UpdateAssetTransactionDto,
    metadata?: ActivityMetadata,
    attachmentFiles?: Express.Multer.File[],
  ): Promise<{ id: string }> {
    const transaction = await this.update(
      userId,
      businessId,
      id,
      updateAssetTransactionDto,
      attachmentFiles,
      metadata,
    );
    return { id: transaction.id };
  }

  // Helper methods for mapping
  private mapToAssetTransactionDto(
    transaction: any,
    attachments?: any[],
  ): AssetTransactionDto {
    return {
      id: transaction.id,
      businessId: transaction.businessId,
      assetId: transaction.assetId,
      transactionType: transaction.transactionType,
      refNo: transaction.refNo,
      receiver: transaction.receiver,
      receiverName: transaction.receiverName,
      fromLocationId: transaction.fromLocationId,
      fromLocationName: transaction.fromLocationName,
      toLocationId: transaction.toLocationId,
      toLocationName: transaction.toLocationName,
      transactionDatetime: transaction.transactionDatetime,
      allocatedUpto: transaction.allocatedUpto,
      reason: transaction.reason,
      parentId: transaction.parentId,
      asset: transaction.assetId
        ? {
            id: transaction.assetId,
            name: transaction.assetName,
            assetTag: transaction.assetTag,
          }
        : undefined,
      attachments: attachments?.map((attachment) => ({
        id: attachment.id,
        fileName: attachment.fileName,
        originalName: attachment.originalName,
        publicUrl: attachment.publicUrl,
        size: attachment.size,
        mimeType: attachment.mimeType,
      })),
      createdBy: transaction.createdByName,
      updatedBy: transaction.updatedByName,
      createdAt: transaction.createdAt,
      updatedAt: transaction.updatedAt,
    };
  }

  private mapToAssetTransactionListDto(
    transaction: any,
  ): AssetTransactionListDto {
    return {
      id: transaction.id,
      assetName: transaction.assetName,
      transactionType: transaction.transactionType,
      refNo: transaction.refNo,
      receiverName: transaction.receiverName,
      fromLocationName: transaction.fromLocationName,
      toLocationName: transaction.toLocationName,
      transactionDatetime: transaction.transactionDatetime,
      allocatedUpto: transaction.allocatedUpto,
      reason: transaction.reason,
      createdAt: transaction.createdAt,
    };
  }

  private mapToAssetTransactionSlimDto(
    transaction: any,
  ): AssetTransactionSlimDto {
    return {
      id: transaction.id,
      refNo: transaction.refNo,
      transactionType: transaction.transactionType,
      assetName: transaction.assetName,
      transactionDatetime: transaction.transactionDatetime,
    };
  }

  private buildAdvancedFilters(filterArray: any[]): any[] {
    const conditions: any[] = [];

    for (const filter of filterArray) {
      const { field, operator, value } = filter;

      if (!field || !operator || value === undefined) {
        continue;
      }

      switch (field) {
        case 'refNo':
          if (operator === 'contains') {
            conditions.push(ilike(assetTransactions.refNo, `%${value}%`));
          } else if (operator === 'equals') {
            conditions.push(eq(assetTransactions.refNo, value));
          }
          break;
        case 'transactionType':
          if (operator === 'equals') {
            conditions.push(eq(assetTransactions.transactionType, value));
          }
          break;
        case 'reason':
          if (operator === 'contains') {
            conditions.push(ilike(assetTransactions.reason, `%${value}%`));
          } else if (operator === 'equals') {
            conditions.push(eq(assetTransactions.reason, value));
          }
          break;
        case 'transactionDatetime':
          if (operator === 'gte') {
            conditions.push(
              gte(assetTransactions.transactionDatetime, new Date(value)),
            );
          } else if (operator === 'lte') {
            conditions.push(
              lte(assetTransactions.transactionDatetime, new Date(value)),
            );
          } else if (operator === 'equals') {
            conditions.push(
              eq(assetTransactions.transactionDatetime, new Date(value)),
            );
          }
          break;
        case 'allocatedUpto':
          if (operator === 'gte') {
            conditions.push(
              gte(
                assetTransactions.allocatedUpto,
                new Date(value).toISOString().split('T')[0],
              ),
            );
          } else if (operator === 'lte') {
            conditions.push(
              lte(
                assetTransactions.allocatedUpto,
                new Date(value).toISOString().split('T')[0],
              ),
            );
          } else if (operator === 'equals') {
            conditions.push(
              eq(
                assetTransactions.allocatedUpto,
                new Date(value).toISOString().split('T')[0],
              ),
            );
          }
          break;
        case 'createdAt':
          if (operator === 'gte') {
            conditions.push(gte(assetTransactions.createdAt, new Date(value)));
          } else if (operator === 'lte') {
            conditions.push(lte(assetTransactions.createdAt, new Date(value)));
          } else if (operator === 'equals') {
            conditions.push(eq(assetTransactions.createdAt, new Date(value)));
          }
          break;
      }
    }

    return conditions;
  }
}

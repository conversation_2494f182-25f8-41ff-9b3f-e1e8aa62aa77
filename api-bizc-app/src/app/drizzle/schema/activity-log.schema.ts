import {
  pgTable,
  pgEnum,
  uuid,
  text,
  timestamp,
  index,
  jsonb,
  integer,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { users } from './users.schema';
import {
  ActivityType,
  EntityType,
  ActivitySource,
} from '../../shared/types/activity.enum';

// Activity Type enum - Enhanced version using shared enum values
export const activityTypeEnum = pgEnum('activity_type', [
  // Single operations
  ActivityType.CREATE,
  ActivityType.UPDATE,
  ActivityType.DELETE,
  ActivityType.RESTORE,
  ActivityType.STATUS_CHANGE,
  ActivityType.PRIORITY_CHANGE,

  // Bulk operations
  ActivityType.BULK_CREATE,
  ActivityType.BULK_UPDATE,
  ActivityType.BULK_DELETE,
  ActivityType.BULK_STATUS_CHANGE,
  ActivityType.BULK_PRIORITY_CHANGE,

  // Auth operations
  ActivityType.LOGIN,
  ActivityType.LOGOUT,
  ActivityType.LOGIN_FAILED,

  // Data operations
  ActivityType.EXPORT,
  ActivityType.IMPORT,
  ActivityType.VIEW,
  ActivityType.SEARCH,
]);

// Activity Source enum - Where the activity originated from
export const activitySourceEnum = pgEnum('activity_source', [
  ActivitySource.WEB,
  ActivitySource.MOBILE,
  ActivitySource.API,
  ActivitySource.IMPORT,
  ActivitySource.SYSTEM,
]);

// Entity Type enum - Using shared enum values
export const entityTypeEnum = pgEnum('entity_type', [
  EntityType.DESIGNATION,
  EntityType.USER,
  EntityType.BUSINESS,
  EntityType.AUTH,
  EntityType.SYSTEM,
  EntityType.ASSET,
  EntityType.ALLOWANCE_TYPE,
  EntityType.ACCOUNT,
  EntityType.CATEGORY,
  EntityType.PRODUCT,
  EntityType.SERVICE,
  EntityType.CUSTOMER,
  EntityType.ORDER,
  EntityType.INVOICE,
  EntityType.PAYMENT,
  EntityType.STAFF,
  EntityType.DEPARTMENT,
  EntityType.LOCATION,
  EntityType.SUPPLIER,
  EntityType.VEHICLE,
  EntityType.RESERVATION,
  EntityType.CAMPAIGN,
  EntityType.TEMPLATE,
  EntityType.WORK_ORDER,
  EntityType.PROJECT,
  EntityType.TASK,
  EntityType.EXPENSE,
  EntityType.LEAD,
  EntityType.ESTIMATE,
  EntityType.MEETING,
  EntityType.ACCOMMODATION_UNIT,
  EntityType.APPOINTMENT,
  EntityType.ASSET_DAMAGE,
  EntityType.ASSET_MAINTENANCE,
  EntityType.ASSET_CATEGORY,
  EntityType.DISCOUNT_PLAN,
  EntityType.EMPLOYEE_SALARY,
  EntityType.EVENT_SPACE,
  EntityType.ASSET_REPAIR_ORDER,
]);

// Enhanced Activity Log table
export const activityLogs = pgTable(
  'activity_logs',
  {
    id: uuid('id').defaultRandom().primaryKey(),

    // Entity information
    entityType: entityTypeEnum('entity_type').notNull(),
    entityId: uuid('entity_id'), // Nullable for bulk operations
    entityIds: jsonb('entity_ids'), // Array of IDs for bulk operations

    // Activity details
    activityType: activityTypeEnum('activity_type').notNull(),
    source: activitySourceEnum('source').default(ActivitySource.WEB).notNull(),
    userId: uuid('user_id')
      .notNull()
      .references(() => users.id),
    businessId: uuid('business_id').references(() => business.id), // Nullable for AUTH operations

    // Change tracking
    changes: jsonb('changes'), // Before/after values
    affectedCount: integer('affected_count'), // Number of records affected (for bulk)

    // Context information
    metadata: jsonb('metadata'), // Additional context
    ipAddress: text('ip_address'),
    userAgent: text('user_agent'),
    sessionId: text('session_id'), // Track actions within same session

    // Performance tracking
    duration: integer('duration'), // Operation duration in milliseconds

    // Timestamps
    createdAt: timestamp('created_at').defaultNow().notNull(),
  },
  (t) => ({
    idIndex: index('activity_logs_id_index').on(t.id),
    entityIndex: index('activity_logs_entity_index').on(
      t.entityType,
      t.entityId,
    ),
    userIdIndex: index('activity_logs_user_id_index').on(t.userId),
    businessIdIndex: index('activity_logs_business_id_index').on(t.businessId),
    activityTypeIndex: index('activity_logs_activity_type_index').on(
      t.activityType,
    ),
    sourceIndex: index('activity_logs_source_index').on(t.source),
    createdAtIndex: index('activity_logs_created_at_index').on(t.createdAt),
    sessionIdIndex: index('activity_logs_session_id_index').on(t.sessionId),
    // Composite index for common queries
    entityActivityIndex: index('activity_logs_entity_activity_index').on(
      t.entityType,
      t.entityId,
      t.activityType,
    ),
    // Index for performance analysis
    durationIndex: index('activity_logs_duration_index').on(t.duration),
  }),
);

// Activity Logs Relations
export const activityLogsRelations = relations(activityLogs, ({ one }) => ({
  business: one(business, {
    fields: [activityLogs.businessId],
    references: [business.id],
  }),
  user: one(users, {
    fields: [activityLogs.userId],
    references: [users.id],
  }),
}));

import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  ConflictException,
  forwardRef,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateTaxDto } from './dto/create-tax.dto';
import { UpdateTaxDto } from './dto/update-tax.dto';
import { TaxDto } from './dto/tax.dto';
import { TaxSlimDto } from './dto/tax-slim.dto';
import { TaxListDto } from './dto/tax-list.dto';
import { taxes, taxGroupItems } from '../drizzle/schema/taxes.schema';
import { users } from '../drizzle/schema/users.schema';
import {
  eq,
  and,
  isNull,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  inArray,
  count,
  or,
} from 'drizzle-orm';
import { TaxStatus, TaxApplicableOn } from '../shared/types';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { ActivityLogName } from '../shared/types';
import { AccountsService } from '../accounts/accounts.service';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class TaxesService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private activityLogService: ActivityLogService,
    @Inject(forwardRef(() => AccountsService))
    private accountsService: AccountsService,
  ) {}

  async create(
    userId: string,
    businessId: string,
    createTaxDto: CreateTaxDto,
  ): Promise<TaxDto> {
    console.log('createTaxDto', createTaxDto);

    // Check if tax name already exists for this business
    const existingTax = await this.db
      .select()
      .from(taxes)
      .where(
        and(
          eq(taxes.businessId, businessId),
          eq(taxes.taxName, createTaxDto.taxName),
          isNull(taxes.deletedAt),
        ),
      )
      .limit(1);

    if (existingTax.length > 0) {
      throw new ConflictException(
        `Tax with name "${createTaxDto.taxName}" already exists`,
      );
    }

    // Shift existing taxes down by 1 position
    await this.db
      .update(taxes)
      .set({
        position: sql`${taxes.position} + 1`,
        updatedAt: new Date(),
      })
      .where(and(eq(taxes.businessId, businessId), isNull(taxes.deletedAt)));

    // Validate required fields
    if (!createTaxDto.taxName || createTaxDto.taxName.trim() === '') {
      throw new BadRequestException('Tax name is required');
    }

    try {
      return await this.db.transaction(async (tx) => {
        // Helper function to convert percentage to decimal
        const convertPercentageToDecimal = (
          percentage: string | undefined | null,
        ): string | null => {
          console.log('percentage VALUE ::: ', percentage);
          if (
            !percentage ||
            percentage === '' ||
            percentage === '0' ||
            percentage === null
          ) {
            return null;
          }
          const numValue = parseFloat(percentage);
          if (isNaN(numValue)) {
            return null;
          }
          // Convert percentage to decimal (e.g., 12.5 -> 0.125)
          return (numValue / 100).toFixed(4);
        };

        // Create the tax
        const [newTax] = await tx
          .insert(taxes)
          .values({
            businessId,
            taxName: createTaxDto.taxName,
            description: createTaxDto.description,
            isGroup: createTaxDto.isGroup || false,
            taxAgencyName: createTaxDto.taxAgencyName,
            businessIdNo: createTaxDto.businessIdNo,
            startOfCurrentTaxPeriod: createTaxDto.startOfCurrentTaxPeriod,
            filingFrequency: createTaxDto.filingFrequency,
            reportingMethod: createTaxDto.reportingMethod,
            isCollectedOnSales: createTaxDto.isCollectedOnSales || false,
            salesRate: convertPercentageToDecimal(createTaxDto.salesRate),
            isCollectedOnPurchases:
              createTaxDto.isCollectedOnPurchases || false,
            purchaseRate: convertPercentageToDecimal(createTaxDto.purchaseRate),
            isPurchaseTaxReclaimable:
              createTaxDto.isPurchaseTaxReclaimable || false,
            status: TaxStatus.ACTIVE,
            createdBy: userId,
          })
          .returning();

        // If it's a tax group, create the group items
        if (
          createTaxDto.isGroup &&
          createTaxDto.groupItems &&
          Array.isArray(createTaxDto.groupItems)
        ) {
          for (const groupItem of createTaxDto.groupItems) {
            if (groupItem.individualTaxId && groupItem.applicableOn) {
              await tx.insert(taxGroupItems).values({
                groupTaxId: newTax.id,
                individualTaxId: groupItem.individualTaxId,
                applicableOn: groupItem.applicableOn,
                orderIndex: groupItem.orderIndex || 1,
              });
            }
          }
        }

        // Create associated tax accounts for individual taxes
        if (!createTaxDto.isGroup) {
          await this.createTaxAccounts(userId, businessId, newTax);
        }

        // Log the activity
        await this.activityLogService.log(
          ActivityLogName.CREATE,
          `Created tax: ${newTax.taxName}`,
          { id: newTax.id, type: 'tax' },
          { id: userId, type: 'user' },
          { taxId: newTax.id, businessId },
        );

        return this.findOne(userId, businessId, newTax.id);
      });
    } catch (error) {
      console.error('Error creating tax:', error);
      if (
        error instanceof BadRequestException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to create tax. Please check your input data.',
      );
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string,
    createTaxDto: CreateTaxDto,
  ): Promise<{ id: string }> {
    const tax = await this.create(userId, businessId, createTaxDto);
    return { id: tax.id };
  }

  async bulkCreate(
    userId: string,
    businessId: string,
    createTaxDtos: CreateTaxDto[],
  ): Promise<TaxDto[]> {
    const results: TaxDto[] = [];

    for (const createTaxDto of createTaxDtos) {
      const tax = await this.create(userId, businessId, createTaxDto);
      results.push(tax);
    }

    return results;
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string,
    createTaxDtos: CreateTaxDto[],
  ): Promise<{ ids: string[] }> {
    const taxes = await this.bulkCreate(userId, businessId, createTaxDtos);
    return { ids: taxes.map((tax) => tax.id) };
  }

  async findAll(
    userId: string,
    businessId: string,
    page: number = 1,
    limit: number = 10,
    from?: string,
    to?: string,
    taxName?: string,
    status?: string,
    isGroup?: string,
    filters?: string,
    joinOperator: 'and' | 'or' = 'and',
    sort?: string,
  ): Promise<{
    data: TaxListDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  }> {
    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(taxes.businessId, businessId),
      isNull(taxes.deletedAt),
    ];

    // Add filters
    if (taxName) {
      whereConditions.push(ilike(taxes.taxName, `%${taxName}%`));
    }

    if (status) {
      whereConditions.push(eq(taxes.status, status as TaxStatus));
    }

    // if (isGroup !== undefined) {
    //   whereConditions.push(eq(taxes.isGroup, isGroup === 'true'));
    // }

    if (from) {
      whereConditions.push(gte(taxes.createdAt, new Date(from)));
    }

    if (to) {
      whereConditions.push(lte(taxes.createdAt, new Date(to)));
    }

    // Handle additional filters
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        for (const filter of parsedFilters) {
          if (filter.id && filter.value !== undefined && filter.value !== '') {
            switch (filter.id) {
              case 'taxName':
                if (filter.operator === 'iLike' || !filter.operator) {
                  whereConditions.push(
                    ilike(taxes.taxName, `%${filter.value}%`),
                  );
                } else if (filter.operator === 'eq') {
                  whereConditions.push(eq(taxes.taxName, filter.value));
                } else if (filter.operator === 'ne') {
                  whereConditions.push(
                    sql`${taxes.taxName} != ${filter.value}`,
                  );
                }
                break;
              case 'description':
                if (filter.operator === 'iLike' || !filter.operator) {
                  whereConditions.push(
                    ilike(taxes.description, `%${filter.value}%`),
                  );
                } else if (filter.operator === 'eq') {
                  whereConditions.push(eq(taxes.description, filter.value));
                } else if (filter.operator === 'ne') {
                  whereConditions.push(
                    sql`${taxes.description} != ${filter.value}`,
                  );
                } else if (filter.operator === 'isEmpty') {
                  whereConditions.push(
                    or(isNull(taxes.description), eq(taxes.description, '')),
                  );
                } else if (filter.operator === 'isNotEmpty') {
                  whereConditions.push(
                    and(
                      sql`${taxes.description} IS NOT NULL`,
                      sql`${taxes.description} != ''`,
                    ),
                  );
                }
                break;
              case 'taxAgencyName':
                if (filter.operator === 'iLike' || !filter.operator) {
                  whereConditions.push(
                    ilike(taxes.taxAgencyName, `%${filter.value}%`),
                  );
                } else if (filter.operator === 'eq') {
                  whereConditions.push(eq(taxes.taxAgencyName, filter.value));
                } else if (filter.operator === 'ne') {
                  whereConditions.push(
                    sql`${taxes.taxAgencyName} != ${filter.value}`,
                  );
                } else if (filter.operator === 'isEmpty') {
                  whereConditions.push(
                    or(
                      isNull(taxes.taxAgencyName),
                      eq(taxes.taxAgencyName, ''),
                    ),
                  );
                } else if (filter.operator === 'isNotEmpty') {
                  whereConditions.push(
                    and(
                      sql`${taxes.taxAgencyName} IS NOT NULL`,
                      sql`${taxes.taxAgencyName} != ''`,
                    ),
                  );
                }
                break;
              case 'businessIdNo':
                if (filter.operator === 'iLike' || !filter.operator) {
                  whereConditions.push(
                    ilike(taxes.businessIdNo, `%${filter.value}%`),
                  );
                } else if (filter.operator === 'eq') {
                  whereConditions.push(eq(taxes.businessIdNo, filter.value));
                } else if (filter.operator === 'ne') {
                  whereConditions.push(
                    sql`${taxes.businessIdNo} != ${filter.value}`,
                  );
                } else if (filter.operator === 'isEmpty') {
                  whereConditions.push(
                    or(isNull(taxes.businessIdNo), eq(taxes.businessIdNo, '')),
                  );
                } else if (filter.operator === 'isNotEmpty') {
                  whereConditions.push(
                    and(
                      sql`${taxes.businessIdNo} IS NOT NULL`,
                      sql`${taxes.businessIdNo} != ''`,
                    ),
                  );
                }
                break;
              case 'status':
                if (filter.operator === 'eq' || !filter.operator) {
                  whereConditions.push(
                    eq(taxes.status, filter.value as TaxStatus),
                  );
                } else if (filter.operator === 'ne') {
                  whereConditions.push(sql`${taxes.status} != ${filter.value}`);
                }
                break;
              case 'isGroup': {
                const isGroupValue =
                  filter.value === 'true' || filter.value === true;
                if (filter.operator === 'eq' || !filter.operator) {
                  whereConditions.push(eq(taxes.isGroup, isGroupValue));
                } else if (filter.operator === 'ne') {
                  whereConditions.push(
                    sql`${taxes.isGroup} != ${isGroupValue}`,
                  );
                }
                break;
              }
            }
          }
        }
      } catch (error) {
        // Invalid JSON, ignore filters
        console.error('Error parsing filters:', error);
      }
    }

    const whereClause =
      joinOperator === 'or' && whereConditions.length > 2
        ? and(
            eq(taxes.businessId, businessId),
            isNull(taxes.deletedAt),
            or(...whereConditions.slice(2)),
          )
        : and(...whereConditions);

    // Build order by
    let orderBy;
    if (sort) {
      const [field, direction] = sort.split(':');
      const isDesc = direction === 'desc';

      switch (field) {
        case 'taxName':
          orderBy = isDesc ? desc(taxes.taxName) : asc(taxes.taxName);
          break;
        case 'status':
          orderBy = isDesc ? desc(taxes.status) : asc(taxes.status);
          break;
        case 'createdAt':
          orderBy = isDesc ? desc(taxes.createdAt) : asc(taxes.createdAt);
          break;
        case 'position':
        default:
          orderBy = isDesc ? desc(taxes.position) : asc(taxes.position);
          break;
      }
    } else {
      orderBy = asc(taxes.position);
    }

    // Get total count
    const [{ total }] = await this.db
      .select({ total: count() })
      .from(taxes)
      .where(whereClause);

    // Get paginated data
    const taxesData = await this.db
      .select({
        id: taxes.id,
        taxName: taxes.taxName,
        description: taxes.description,
        isGroup: taxes.isGroup,
        taxAgencyName: taxes.taxAgencyName,
        businessIdNo: taxes.businessIdNo,
        startOfCurrentTaxPeriod: taxes.startOfCurrentTaxPeriod,
        filingFrequency: taxes.filingFrequency,
        reportingMethod: taxes.reportingMethod,
        isCollectedOnSales: taxes.isCollectedOnSales,
        salesRate: taxes.salesRate,
        isCollectedOnPurchases: taxes.isCollectedOnPurchases,
        purchaseRate: taxes.purchaseRate,
        isPurchaseTaxReclaimable: taxes.isPurchaseTaxReclaimable,
        position: taxes.position,
        status: taxes.status,
      })
      .from(taxes)
      .where(whereClause)
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    // Get group items count for tax groups
    const taxIds = taxesData.map((tax) => tax.id);
    const groupItemsCounts = taxIds.length
      ? await this.db
          .select({
            groupTaxId: taxGroupItems.groupTaxId,
            count: count(),
          })
          .from(taxGroupItems)
          .where(inArray(taxGroupItems.groupTaxId, taxIds))
          .groupBy(taxGroupItems.groupTaxId)
      : [];

    const groupItemsCountMap = new Map(
      groupItemsCounts.map((item) => [item.groupTaxId, item.count]),
    );

    const data: TaxListDto[] = taxesData.map((tax) => ({
      ...tax,
      groupItemsCount: tax.isGroup
        ? groupItemsCountMap.get(tax.id) || 0
        : undefined,
    }));

    // Group the data by type for better organization
    const groupedData = this.organizeDataByGroups(data);

    const totalPages = Math.ceil(total / limit);

    return {
      data: groupedData,
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
    };
  }

  async findAllOptimized(
    userId: string,
    businessId: string,
    page?: number,
    limit?: number,
    from?: string,
    to?: string,
    taxName?: string,
    status?: string,
    isGroup?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ) {
    return this.findAll(
      userId,
      businessId,
      page || 1,
      limit || 10,
      from,
      to,
      taxName,
      status,
      isGroup,
      filters,
      joinOperator || 'and',
      sort,
    );
  }

  async findOne(
    userId: string,
    businessId: string,
    id: string,
  ): Promise<TaxDto> {
    const tax = await this.db
      .select({
        id: taxes.id,
        businessId: taxes.businessId,
        taxName: taxes.taxName,
        description: taxes.description,
        isGroup: taxes.isGroup,
        taxAgencyName: taxes.taxAgencyName,
        businessIdNo: taxes.businessIdNo,
        startOfCurrentTaxPeriod: taxes.startOfCurrentTaxPeriod,
        filingFrequency: taxes.filingFrequency,
        reportingMethod: taxes.reportingMethod,
        isCollectedOnSales: taxes.isCollectedOnSales,
        salesRate: taxes.salesRate,
        isCollectedOnPurchases: taxes.isCollectedOnPurchases,
        purchaseRate: taxes.purchaseRate,
        isPurchaseTaxReclaimable: taxes.isPurchaseTaxReclaimable,
        position: taxes.position,
        status: taxes.status,
        createdBy: taxes.createdBy,
        updatedBy: taxes.updatedBy,
        createdAt: taxes.createdAt,
        updatedAt: taxes.updatedAt,
      })
      .from(taxes)
      .leftJoin(users, eq(taxes.createdBy, users.id))
      .where(
        and(
          eq(taxes.id, id),
          eq(taxes.businessId, businessId),
          isNull(taxes.deletedAt),
        ),
      )
      .limit(1);

    if (tax.length === 0) {
      throw new NotFoundException('Tax not found');
    }

    const taxData = tax[0];

    // Get group items if it's a tax group
    let groupItems = undefined;
    if (taxData.isGroup) {
      const groupItemsData = await this.db
        .select({
          id: taxGroupItems.id,
          individualTaxId: taxGroupItems.individualTaxId,
          individualTaxName: taxes.taxName,
          applicableOn: taxGroupItems.applicableOn,
          orderIndex: taxGroupItems.orderIndex,
          createdAt: taxGroupItems.createdAt,
          updatedAt: taxGroupItems.updatedAt,
        })
        .from(taxGroupItems)
        .leftJoin(taxes, eq(taxGroupItems.individualTaxId, taxes.id))
        .where(eq(taxGroupItems.groupTaxId, id))
        .orderBy(asc(taxGroupItems.orderIndex));

      groupItems = groupItemsData;
    }

    // Helper function to convert decimal to percentage
    const convertDecimalToPercentage = (
      decimal: string | null,
    ): string | undefined => {
      if (!decimal || decimal === '0' || decimal === '0.0000') {
        return undefined;
      }
      const numValue = parseFloat(decimal);
      if (isNaN(numValue)) {
        return undefined;
      }
      // Convert decimal to percentage (e.g., 0.125 -> 12.5)
      return (numValue * 100).toString();
    };

    return {
      id: taxData.id,
      businessId: taxData.businessId,
      taxName: taxData.taxName,
      description: taxData.description,
      isGroup: taxData.isGroup,
      taxAgencyName: taxData.taxAgencyName,
      businessIdNo: taxData.businessIdNo,
      startOfCurrentTaxPeriod: taxData.startOfCurrentTaxPeriod,
      filingFrequency: taxData.filingFrequency,
      reportingMethod: taxData.reportingMethod,
      isCollectedOnSales: taxData.isCollectedOnSales,
      salesRate: convertDecimalToPercentage(taxData.salesRate),
      isCollectedOnPurchases: taxData.isCollectedOnPurchases,
      purchaseRate: convertDecimalToPercentage(taxData.purchaseRate),
      isPurchaseTaxReclaimable: taxData.isPurchaseTaxReclaimable,
      position: taxData.position,
      status: taxData.status,
      groupItems,
    };
  }

  async findSlim(
    userId: string,
    businessId: string,
    status?: TaxStatus,
  ): Promise<TaxSlimDto[]> {
    const whereConditions = [
      eq(taxes.businessId, businessId),
      isNull(taxes.deletedAt),
    ];

    if (status) {
      whereConditions.push(eq(taxes.status, status));
    }

    const taxesData = await this.db
      .select({
        id: taxes.id,
        taxName: taxes.taxName,
        isGroup: taxes.isGroup,
        status: taxes.status,
      })
      .from(taxes)
      .where(and(...whereConditions))
      .orderBy(asc(taxes.position));

    return taxesData;
  }

  async update(
    userId: string,
    businessId: string,
    id: string,
    updateTaxDto: UpdateTaxDto,
  ): Promise<TaxDto> {
    // Check if tax exists
    const existingTax = await this.db
      .select()
      .from(taxes)
      .where(
        and(
          eq(taxes.id, id),
          eq(taxes.businessId, businessId),
          isNull(taxes.deletedAt),
        ),
      )
      .limit(1);

    if (existingTax.length === 0) {
      throw new NotFoundException('Tax not found');
    }

    // Check if tax name already exists for this business (excluding current tax)
    if (updateTaxDto.taxName) {
      const existingTaxWithName = await this.db
        .select()
        .from(taxes)
        .where(
          and(
            eq(taxes.businessId, businessId),
            eq(taxes.taxName, updateTaxDto.taxName),
            sql`${taxes.id} != ${id}`,
            isNull(taxes.deletedAt),
          ),
        )
        .limit(1);

      if (existingTaxWithName.length > 0) {
        throw new ConflictException(
          `Tax with name "${updateTaxDto.taxName}" already exists`,
        );
      }
    }

    // Validate required fields if they are being updated
    if (
      updateTaxDto.taxName !== undefined &&
      (!updateTaxDto.taxName || updateTaxDto.taxName.trim() === '')
    ) {
      throw new BadRequestException('Tax name cannot be empty');
    }

    try {
      return await this.db.transaction(async (tx) => {
        // Helper function to convert percentage to decimal
        const convertPercentageToDecimal = (
          percentage: string | undefined | null,
        ): string | null => {
          if (
            !percentage ||
            percentage === '' ||
            percentage === '0' ||
            percentage === null
          ) {
            return null;
          }
          const numValue = parseFloat(percentage);
          if (isNaN(numValue)) {
            return null;
          }
          // Convert percentage to decimal (e.g., 12.5 -> 0.125)
          return (numValue / 100).toFixed(4);
        };

        // Prepare update data with converted rates
        const updateData: any = {
          ...updateTaxDto,
          updatedBy: userId,
          updatedAt: new Date(),
        };

        // Handle rate conversions only if they are provided
        if (updateTaxDto.salesRate !== undefined) {
          updateData.salesRate = convertPercentageToDecimal(
            updateTaxDto.salesRate,
          );
        }
        if (updateTaxDto.purchaseRate !== undefined) {
          updateData.purchaseRate = convertPercentageToDecimal(
            updateTaxDto.purchaseRate,
          );
        }

        // Remove undefined values to avoid overwriting with null
        Object.keys(updateData).forEach((key) => {
          if (updateData[key] === undefined) {
            delete updateData[key];
          }
        });

        // Update the tax
        await tx.update(taxes).set(updateData).where(eq(taxes.id, id));

        // Handle tax account changes for individual taxes
        if (!updateTaxDto.isGroup) {
          await this.handleTaxAccountUpdates(
            userId, 
            businessId, 
            id, 
            existingTax[0], 
            updateData
          );
        }

        // Handle group items if it's a tax group
        if (
          updateTaxDto.isGroup &&
          updateTaxDto.groupItems &&
          Array.isArray(updateTaxDto.groupItems)
        ) {
          // Delete existing group items
          await tx
            .delete(taxGroupItems)
            .where(eq(taxGroupItems.groupTaxId, id));

          // Insert new group items
          for (const groupItem of updateTaxDto.groupItems) {
            if (groupItem.individualTaxId && groupItem.applicableOn) {
              await tx.insert(taxGroupItems).values({
                groupTaxId: id,
                individualTaxId: groupItem.individualTaxId,
                applicableOn: groupItem.applicableOn,
                orderIndex: groupItem.orderIndex || 1,
              });
            }
          }
        }

        // Log the activity
        await this.activityLogService.log(
          ActivityLogName.UPDATE,
          `Updated tax: ${updateTaxDto.taxName || existingTax[0].taxName}`,
          { id: id, type: 'tax' },
          { id: userId, type: 'user' },
          { taxId: id, businessId },
        );

        return this.findOne(userId, businessId, id);
      });
    } catch (error) {
      console.error('Error updating tax:', error);
      if (
        error instanceof BadRequestException ||
        error instanceof ConflictException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to update tax. Please check your input data.',
      );
    }
  }

  async updateAndReturnId(
    userId: string,
    businessId: string,
    id: string,
    updateTaxDto: UpdateTaxDto,
  ): Promise<{ id: string }> {
    await this.update(userId, businessId, id, updateTaxDto);
    return { id };
  }

  async remove(
    userId: string,
    businessId: string,
    id: string,
  ): Promise<{ id: string }> {
    // Check if tax exists
    const existingTax = await this.db
      .select()
      .from(taxes)
      .where(
        and(
          eq(taxes.id, id),
          eq(taxes.businessId, businessId),
          isNull(taxes.deletedAt),
        ),
      )
      .limit(1);

    if (existingTax.length === 0) {
      throw new NotFoundException('Tax not found');
    }

    return await this.db.transaction(async (tx) => {
      // Soft delete the tax
      await tx
        .update(taxes)
        .set({
          deletedBy: userId,
          deletedAt: new Date(),
          updatedAt: new Date(),
        })
        .where(eq(taxes.id, id));

      // Delete group items if it's a tax group
      if (existingTax[0].isGroup) {
        await tx.delete(taxGroupItems).where(eq(taxGroupItems.groupTaxId, id));
      }

      // Deactivate associated tax accounts for individual taxes
      if (!existingTax[0].isGroup) {
        const metadata: ActivityMetadata = { source: 'WEB' };
        
        if (existingTax[0].salesTaxPayableAccountId) {
          await this.accountsService.deactivateTaxAccount(
            userId,
            businessId,
            existingTax[0].salesTaxPayableAccountId,
            metadata,
          );
        }
        
        if (existingTax[0].purchaseTaxReceivableAccountId) {
          await this.accountsService.deactivateTaxAccount(
            userId,
            businessId,
            existingTax[0].purchaseTaxReceivableAccountId,
            metadata,
          );
        }
        
        if (existingTax[0].purchaseTaxExpenseAccountId) {
          await this.accountsService.deactivateTaxAccount(
            userId,
            businessId,
            existingTax[0].purchaseTaxExpenseAccountId,
            metadata,
          );
        }
      }

      // Log the activity
      await this.activityLogService.log(
        ActivityLogName.DELETE,
        `Deleted tax: ${existingTax[0].taxName}`,
        { id: id, type: 'tax' },
        { id: userId, type: 'user' },
        { taxId: id, businessId },
      );

      return { id };
    });
  }

  async bulkDelete(
    userId: string,
    businessId: string,
    ids: string[],
  ): Promise<{ deletedCount: number; deletedIds: string[] }> {
    const deletedIds: string[] = [];

    for (const id of ids) {
      try {
        await this.remove(userId, businessId, id);
        deletedIds.push(id);
      } catch (error) {
        // Continue with other deletions even if one fails
        console.error(`Failed to delete tax ${id}:`, error);
      }
    }

    return {
      deletedCount: deletedIds.length,
      deletedIds,
    };
  }

  async checkTaxNameAvailability(
    userId: string,
    businessId: string,
    taxName: string,
    excludeId?: string,
  ): Promise<{ available: boolean }> {
    const whereConditions = [
      eq(taxes.businessId, businessId),
      eq(taxes.taxName, taxName),
      isNull(taxes.deletedAt),
    ];

    if (excludeId) {
      whereConditions.push(sql`${taxes.id} != ${excludeId}`);
    }

    const existingTax = await this.db
      .select()
      .from(taxes)
      .where(and(...whereConditions))
      .limit(1);

    return { available: existingTax.length === 0 };
  }

  /**
   * Handles tax account updates when tax settings change
   */
  private async handleTaxAccountUpdates(
    userId: string,
    businessId: string,
    taxId: string,
    existingTax: any,
    updateData: any,
  ): Promise<void> {
    const metadata: ActivityMetadata = { source: 'WEB' };
    const accountUpdates: any = {};

    // Handle sales tax collection changes
    if (updateData.isCollectedOnSales !== undefined) {
      if (updateData.isCollectedOnSales && !existingTax.isCollectedOnSales) {
        // Creating sales tax collection - create account
        const salesAccount = await this.accountsService.createSalesTaxPayableAccount(
          userId,
          businessId,
          taxId,
          updateData.taxName || existingTax.taxName,
          metadata,
        );
        accountUpdates.salesTaxPayableAccountId = salesAccount.id;
      } else if (!updateData.isCollectedOnSales && existingTax.isCollectedOnSales) {
        // Disabling sales tax collection - deactivate account
        if (existingTax.salesTaxPayableAccountId) {
          await this.accountsService.deactivateTaxAccount(
            userId,
            businessId,
            existingTax.salesTaxPayableAccountId,
            metadata,
          );
          accountUpdates.salesTaxPayableAccountId = null;
        }
      }
    }

    // Handle purchase tax collection changes
    if (updateData.isCollectedOnPurchases !== undefined) {
      if (updateData.isCollectedOnPurchases && !existingTax.isCollectedOnPurchases) {
        // Creating purchase tax collection - create appropriate account
        const isReclaimable = updateData.isPurchaseTaxReclaimable ?? existingTax.isPurchaseTaxReclaimable;
        
        if (isReclaimable) {
          const receivableAccount = await this.accountsService.createPurchaseTaxReceivableAccount(
            userId,
            businessId,
            taxId,
            updateData.taxName || existingTax.taxName,
            metadata,
          );
          accountUpdates.purchaseTaxReceivableAccountId = receivableAccount.id;
        } else {
          const expenseAccount = await this.accountsService.createPurchaseTaxExpenseAccount(
            userId,
            businessId,
            taxId,
            updateData.taxName || existingTax.taxName,
            metadata,
          );
          accountUpdates.purchaseTaxExpenseAccountId = expenseAccount.id;
        }
      } else if (!updateData.isCollectedOnPurchases && existingTax.isCollectedOnPurchases) {
        // Disabling purchase tax collection - deactivate accounts
        if (existingTax.purchaseTaxReceivableAccountId) {
          await this.accountsService.deactivateTaxAccount(
            userId,
            businessId,
            existingTax.purchaseTaxReceivableAccountId,
            metadata,
          );
          accountUpdates.purchaseTaxReceivableAccountId = null;
        }
        if (existingTax.purchaseTaxExpenseAccountId) {
          await this.accountsService.deactivateTaxAccount(
            userId,
            businessId,
            existingTax.purchaseTaxExpenseAccountId,
            metadata,
          );
          accountUpdates.purchaseTaxExpenseAccountId = null;
        }
      }
    }

    // Handle purchase tax reclaimable setting changes
    if (updateData.isPurchaseTaxReclaimable !== undefined && 
        existingTax.isCollectedOnPurchases && 
        updateData.isPurchaseTaxReclaimable !== existingTax.isPurchaseTaxReclaimable) {
      
      if (updateData.isPurchaseTaxReclaimable) {
        // Switching to reclaimable - deactivate expense account, create receivable account
        if (existingTax.purchaseTaxExpenseAccountId) {
          await this.accountsService.deactivateTaxAccount(
            userId,
            businessId,
            existingTax.purchaseTaxExpenseAccountId,
            metadata,
          );
          accountUpdates.purchaseTaxExpenseAccountId = null;
        }
        
        const receivableAccount = await this.accountsService.createPurchaseTaxReceivableAccount(
          userId,
          businessId,
          taxId,
          updateData.taxName || existingTax.taxName,
          metadata,
        );
        accountUpdates.purchaseTaxReceivableAccountId = receivableAccount.id;
      } else {
        // Switching to non-reclaimable - deactivate receivable account, create expense account
        if (existingTax.purchaseTaxReceivableAccountId) {
          await this.accountsService.deactivateTaxAccount(
            userId,
            businessId,
            existingTax.purchaseTaxReceivableAccountId,
            metadata,
          );
          accountUpdates.purchaseTaxReceivableAccountId = null;
        }
        
        const expenseAccount = await this.accountsService.createPurchaseTaxExpenseAccount(
          userId,
          businessId,
          taxId,
          updateData.taxName || existingTax.taxName,
          metadata,
        );
        accountUpdates.purchaseTaxExpenseAccountId = expenseAccount.id;
      }
    }

    // Update tax record with new account references
    if (Object.keys(accountUpdates).length > 0) {
      await this.db
        .update(taxes)
        .set({
          ...accountUpdates,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(taxes.id, taxId));
    }
  }

  /**
   * Creates associated tax accounts for individual taxes
   */
  private async createTaxAccounts(
    userId: string,
    businessId: string,
    tax: any,
  ): Promise<void> {
    const updateData: any = {};

    // Create Sales Tax Payable account if tax is collected on sales
    if (tax.isCollectedOnSales) {
      const salesAccount = await this.accountsService.createSalesTaxPayableAccount(
        userId,
        businessId,
        tax.id,
        tax.taxName,
        { source: 'WEB' },
      );
      updateData.salesTaxPayableAccountId = salesAccount.id;
    }

    // Create Purchase Tax Receivable account if tax is collected on purchases and reclaimable
    if (tax.isCollectedOnPurchases && tax.isPurchaseTaxReclaimable) {
      const receivableAccount = await this.accountsService.createPurchaseTaxReceivableAccount(
        userId,
        businessId,
        tax.id,
        tax.taxName,
        { source: 'WEB' },
      );
      updateData.purchaseTaxReceivableAccountId = receivableAccount.id;
    }

    // Create Purchase Tax Expense account if tax is not reclaimable
    if (tax.isCollectedOnPurchases && !tax.isPurchaseTaxReclaimable) {
      const expenseAccount = await this.accountsService.createPurchaseTaxExpenseAccount(
        userId,
        businessId,
        tax.id,
        tax.taxName,
        { source: 'WEB' },
      );
      updateData.purchaseTaxExpenseAccountId = expenseAccount.id;
    }

    // Update tax with account references
    if (Object.keys(updateData).length > 0) {
      await this.db
        .update(taxes)
        .set({
          ...updateData,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(taxes.id, tax.id));
    }
  }

  /**
   * Calculates tax for a given amount
   */
  async calculateTax(
    businessId: string,
    taxId: string,
    amount: number,
    isSales: boolean = true,
  ): Promise<{
    taxAmount: number;
    totalAmount: number;
    breakdown: Array<{
      taxId: string;
      taxName: string;
      rate: number;
      amount: number;
    }>;
  }> {
    const tax = await this.db
      .select()
      .from(taxes)
      .where(
        and(
          eq(taxes.id, taxId),
          eq(taxes.businessId, businessId),
          isNull(taxes.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!tax) {
      throw new NotFoundException('Tax not found');
    }

    if (!tax.isGroup) {
      // Simple tax calculation
      const rateDecimal = isSales ? tax.salesRate : tax.purchaseRate;
      const rate = rateDecimal ? parseFloat(rateDecimal) * 100 : 0;
      const taxAmount = amount * (rate / 100);

      return {
        taxAmount,
        totalAmount: amount + taxAmount,
        breakdown: [{
          taxId: tax.id,
          taxName: tax.taxName,
          rate,
          amount: taxAmount,
        }],
      };
    } else {
      // Group tax calculation
      const groupItems = await this.db
        .select({
          id: taxGroupItems.id,
          individualTaxId: taxGroupItems.individualTaxId,
          applicableOn: taxGroupItems.applicableOn,
          orderIndex: taxGroupItems.orderIndex,
          taxName: taxes.taxName,
          salesRate: taxes.salesRate,
          purchaseRate: taxes.purchaseRate,
        })
        .from(taxGroupItems)
        .leftJoin(taxes, eq(taxGroupItems.individualTaxId, taxes.id))
        .where(eq(taxGroupItems.groupTaxId, tax.id))
        .orderBy(asc(taxGroupItems.orderIndex));

      let runningTotal = amount;
      const breakdown = [];
      let totalTaxAmount = 0;

      for (const item of groupItems) {
        const rateDecimal = isSales ? item.salesRate : item.purchaseRate;
        const rate = rateDecimal ? parseFloat(rateDecimal) * 100 : 0;
        
        let baseAmount = amount;
        if (item.applicableOn === TaxApplicableOn.TAX_AMOUNT) {
          baseAmount = totalTaxAmount;
        } else if (item.applicableOn === TaxApplicableOn.NET_PLUS_TAX_AMOUNT) {
          baseAmount = runningTotal;
        }

        const taxAmount = baseAmount * (rate / 100);
        totalTaxAmount += taxAmount;
        runningTotal = amount + totalTaxAmount;

        breakdown.push({
          taxId: item.individualTaxId,
          taxName: item.taxName,
          rate,
          amount: taxAmount,
        });
      }

      return {
        taxAmount: totalTaxAmount,
        totalAmount: amount + totalTaxAmount,
        breakdown,
      };
    }
  }

  /**
   * Finds accounts associated with a tax
   */
  async findTaxAccounts(businessId: string, taxId: string) {
    return this.accountsService.findAccountsByTaxId(businessId, taxId);
  }

  /**
   * Organizes tax data by groups for better display and management
   * Tax groups are displayed first, followed by individual taxes
   * Within each category, items are sorted by position
   */
  private organizeDataByGroups(data: TaxListDto[]): TaxListDto[] {
    // Separate tax groups and individual taxes
    const taxGroups = data.filter((tax) => tax.isGroup);
    const individualTaxes = data.filter((tax) => !tax.isGroup);

    // Sort each category by position
    taxGroups.sort((a, b) => a.position - b.position);
    individualTaxes.sort((a, b) => a.position - b.position);

    // Return tax groups first, then individual taxes
    return [...taxGroups, ...individualTaxes];
  }
}

import {
  Injectable,
  Inject,
  NotFoundException,
  ConflictException,
  UnauthorizedException,
  BadRequestException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { and, eq, desc, asc, ilike, inArray, count, sql } from 'drizzle-orm';
import { attendance, staffMembers } from '../drizzle/schema/schema';
import { CreateAttendanceDto } from './dto/create-attendance.dto';
import { UpdateAttendanceDto } from './dto/update-attendance.dto';
import { AttendanceDto } from './dto/attendance.dto';
import { AttendanceListDto } from './dto/attendance-list.dto';
import {
  PaginatedAttendanceResponseDto,
  DeleteAttendanceResponseDto,
  BulkDeleteAttendanceResponseDto,
} from './dto/attendance-response.dto';
import { AttendanceStatus } from '../shared/types';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { ActivityLogName } from '../shared/types';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class AttendanceService {
  constructor(
    @Inject(DRIZZLE) private readonly db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createAttendanceDto: CreateAttendanceDto,
    metadata?: ActivityMetadata,
  ): Promise<{ attendanceId: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if staff member exists and belongs to the business
      const staffMember = await this.db
        .select()
        .from(staffMembers)
        .where(
          and(
            eq(staffMembers.id, createAttendanceDto.staffId),
            eq(staffMembers.businessId, businessId),
            eq(staffMembers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!staffMember) {
        throw new NotFoundException('Staff member not found');
      }

      // Check if attendance record already exists for this staff member on this date
      const existingAttendance = await this.db
        .select()
        .from(attendance)
        .where(
          and(
            eq(attendance.staffId, createAttendanceDto.staffId),
            eq(attendance.attendanceDate, createAttendanceDto.attendanceDate),
            eq(attendance.businessId, businessId),
            eq(attendance.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingAttendance) {
        throw new ConflictException(
          `Attendance record already exists for ${staffMember.displayName} on ${createAttendanceDto.attendanceDate}`,
        );
      }

      // Create new attendance record
      const [newAttendance] = await this.db
        .insert(attendance)
        .values({
          businessId,
          staffId: createAttendanceDto.staffId,
          attendanceDate: createAttendanceDto.attendanceDate,
          checkInTime: createAttendanceDto.checkInTime,
          checkOutTime: createAttendanceDto.checkOutTime,
          hoursWorked: createAttendanceDto.hoursWorked,
          status: createAttendanceDto.status,
          remarks: createAttendanceDto.remarks,
          createdBy: userId,
        })
        .returning();

      // Log the attendance creation activity
      await this.activityLogService.log(
        ActivityLogName.CREATE,
        `Attendance record created for ${staffMember.displayName} on ${createAttendanceDto.attendanceDate}`,
        { id: newAttendance.attendanceId.toString(), type: 'attendance' },
        { id: userId, type: 'user' },
        { attendanceId: newAttendance.attendanceId, businessId },
      );

      return {
        attendanceId: newAttendance.attendanceId,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create attendance record');
    }
  }

  async findAll(
    businessId: string | null,
    page: number = 1,
    limit: number = 10,
    search?: string,
    sortBy: string = 'attendanceDate',
    sortOrder: 'asc' | 'desc' = 'desc',
    status?: AttendanceStatus,
    staffId?: string,
    startDate?: string,
    endDate?: string,
  ): Promise<PaginatedAttendanceResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(attendance.businessId, businessId),
      eq(attendance.isDeleted, false),
    ];

    if (status) {
      whereConditions.push(eq(attendance.status, status));
    }

    if (staffId) {
      whereConditions.push(eq(attendance.staffId, staffId));
    }

    if (startDate) {
      whereConditions.push(sql`${attendance.attendanceDate} >= ${startDate}`);
    }

    if (endDate) {
      whereConditions.push(sql`${attendance.attendanceDate} <= ${endDate}`);
    }

    // Build search condition
    let searchCondition: any;
    if (search) {
      searchCondition = ilike(staffMembers.displayName, `%${search}%`);
    }

    // Build sort condition
    let sortCondition: any;
    if (sortBy === 'staffName') {
      sortCondition =
        sortOrder === 'asc'
          ? asc(staffMembers.displayName)
          : desc(staffMembers.displayName);
    } else if (sortBy === 'attendanceDate') {
      sortCondition =
        sortOrder === 'asc'
          ? asc(attendance.attendanceDate)
          : desc(attendance.attendanceDate);
    } else if (sortBy === 'status') {
      sortCondition =
        sortOrder === 'asc' ? asc(attendance.status) : desc(attendance.status);
    } else if (sortBy === 'createdAt') {
      sortCondition =
        sortOrder === 'asc'
          ? asc(attendance.createdAt)
          : desc(attendance.createdAt);
    } else {
      // Default to attendanceDate if sortBy is not recognized
      sortCondition =
        sortOrder === 'asc'
          ? asc(attendance.attendanceDate)
          : desc(attendance.attendanceDate);
    }

    // Get total count
    const totalQuery = this.db
      .select({ count: count() })
      .from(attendance)
      .leftJoin(staffMembers, eq(attendance.staffId, staffMembers.id))
      .where(and(...whereConditions, searchCondition));

    const totalResult = await totalQuery;
    const total = totalResult[0]?.count || 0;

    // Get paginated data
    const dataQuery = this.db
      .select({
        attendanceId: attendance.attendanceId,
        staffId: attendance.staffId,
        staffDisplayName: staffMembers.displayName,
        staffEmployeeId: staffMembers.employeeId,
        attendanceDate: attendance.attendanceDate,
        checkInTime: attendance.checkInTime,
        checkOutTime: attendance.checkOutTime,
        hoursWorked: attendance.hoursWorked,
        status: attendance.status,
        remarks: attendance.remarks,
        createdAt: attendance.createdAt,
        updatedAt: attendance.updatedAt,
      })
      .from(attendance)
      .leftJoin(staffMembers, eq(attendance.staffId, staffMembers.id))
      .where(and(...whereConditions, searchCondition))
      .orderBy(sortCondition)
      .limit(limit)
      .offset(offset);

    const data = await dataQuery;

    const totalPages = Math.ceil(total / limit);

    return {
      data: data as AttendanceListDto[],
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(
    businessId: string | null,
    attendanceId: number,
  ): Promise<AttendanceDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const attendanceRecord = await this.db
      .select({
        attendanceId: attendance.attendanceId,
        businessId: attendance.businessId,
        staffId: attendance.staffId,
        staffName: sql<string>`CONCAT(${staffMembers.firstName}, ' ', ${staffMembers.lastName})`,
        staffDisplayName: staffMembers.displayName,
        staffEmployeeId: staffMembers.employeeId,
        attendanceDate: attendance.attendanceDate,
        checkInTime: attendance.checkInTime,
        checkOutTime: attendance.checkOutTime,
        hoursWorked: attendance.hoursWorked,
        status: attendance.status,
        remarks: attendance.remarks,
        createdBy: attendance.createdBy,
        updatedBy: attendance.updatedBy,
        createdAt: attendance.createdAt,
        updatedAt: attendance.updatedAt,
      })
      .from(attendance)
      .leftJoin(staffMembers, eq(attendance.staffId, staffMembers.id))
      .where(
        and(
          eq(attendance.attendanceId, attendanceId),
          eq(attendance.businessId, businessId),
          eq(attendance.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!attendanceRecord) {
      throw new NotFoundException('Attendance record not found');
    }

    return attendanceRecord as AttendanceDto;
  }

  async update(
    userId: string,
    businessId: string | null,
    attendanceId: number,
    updateAttendanceDto: UpdateAttendanceDto,
    metadata?: ActivityMetadata,
  ): Promise<{ attendanceId: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if attendance record exists
      const existingAttendance = await this.db
        .select()
        .from(attendance)
        .where(
          and(
            eq(attendance.attendanceId, attendanceId),
            eq(attendance.businessId, businessId),
            eq(attendance.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingAttendance) {
        throw new NotFoundException('Attendance record not found');
      }

      // Get staff member details for logging
      const staffMember = await this.db
        .select()
        .from(staffMembers)
        .where(eq(staffMembers.id, existingAttendance.staffId))
        .then((results) => results[0]);

      // If updating attendance date, check for conflicts
      if (
        updateAttendanceDto.attendanceDate &&
        updateAttendanceDto.attendanceDate !== existingAttendance.attendanceDate
      ) {
        const conflictingAttendance = await this.db
          .select()
          .from(attendance)
          .where(
            and(
              eq(attendance.staffId, existingAttendance.staffId),
              eq(attendance.attendanceDate, updateAttendanceDto.attendanceDate),
              eq(attendance.businessId, businessId),
              eq(attendance.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (conflictingAttendance) {
          throw new ConflictException(
            `Attendance record already exists for ${staffMember?.displayName} on ${updateAttendanceDto.attendanceDate}`,
          );
        }
      }

      // Update attendance record
      const [updatedAttendance] = await this.db
        .update(attendance)
        .set({
          ...updateAttendanceDto,
          updatedBy: userId,
        })
        .where(eq(attendance.attendanceId, attendanceId))
        .returning();

      // Log the attendance update activity
      await this.activityLogService.log(
        ActivityLogName.UPDATE,
        `Attendance record updated for ${staffMember?.displayName} on ${updatedAttendance.attendanceDate}`,
        { id: updatedAttendance.attendanceId.toString(), type: 'attendance' },
        { id: userId, type: 'user' },
        { attendanceId: updatedAttendance.attendanceId, businessId },
      );

      return {
        attendanceId: updatedAttendance.attendanceId,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to update attendance record');
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    attendanceId: number,
    metadata?: ActivityMetadata,
  ): Promise<DeleteAttendanceResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if attendance record exists
    const existingAttendance = await this.db
      .select()
      .from(attendance)
      .leftJoin(staffMembers, eq(attendance.staffId, staffMembers.id))
      .where(
        and(
          eq(attendance.attendanceId, attendanceId),
          eq(attendance.businessId, businessId),
          eq(attendance.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingAttendance) {
      throw new NotFoundException('Attendance record not found');
    }

    // Soft delete the attendance record
    await this.db
      .update(attendance)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(attendance.attendanceId, attendanceId));

    // Log the attendance deletion activity
    await this.activityLogService.log(
      ActivityLogName.DELETE,
      `Attendance record deleted for ${existingAttendance.staff_members?.displayName} on ${existingAttendance.attendance.attendanceDate}`,
      { id: attendanceId.toString(), type: 'attendance' },
      { id: userId, type: 'user' },
      { attendanceId, businessId },
    );

    return {
      message: 'Attendance record deleted successfully',
    };
  }

  async bulkRemove(
    userId: string,
    businessId: string | null,
    attendanceIds: number[],
    metadata?: ActivityMetadata,
  ): Promise<BulkDeleteAttendanceResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!attendanceIds || attendanceIds.length === 0) {
      throw new BadRequestException('No attendance IDs provided');
    }

    // Check if all attendance records exist
    const existingAttendance = await this.db
      .select()
      .from(attendance)
      .where(
        and(
          inArray(attendance.attendanceId, attendanceIds),
          eq(attendance.businessId, businessId),
          eq(attendance.isDeleted, false),
        ),
      );

    if (existingAttendance.length !== attendanceIds.length) {
      throw new NotFoundException('One or more attendance records not found');
    }

    // Soft delete all attendance records
    await this.db
      .update(attendance)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(inArray(attendance.attendanceId, attendanceIds));

    // Log the bulk deletion activity
    await this.activityLogService.log(
      ActivityLogName.DELETE,
      `Bulk deleted ${attendanceIds.length} attendance records`,
      { id: attendanceIds.join(','), type: 'attendance' },
      { id: userId, type: 'user' },
      { attendanceIds, businessId },
    );

    return {
      message: 'Attendance records deleted successfully',
      deletedCount: attendanceIds.length,
    };
  }
}

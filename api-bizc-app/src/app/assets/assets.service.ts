import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateAssetDto } from './dto/create-asset.dto';
import { UpdateAssetDto } from './dto/update-asset.dto';
import { AssetDto } from './dto/asset.dto';
import { AssetSlimDto } from './dto/asset-slim.dto';
import { AssetListDto } from './dto/asset-list.dto';
import { AssetIdResponseDto } from './dto/asset-id-response.dto';
import { BulkAssetIdsResponseDto } from './dto/bulk-asset-ids-response.dto';
import { BulkDeleteAssetResponseDto } from './dto/bulk-delete-asset-response.dto';
import { BulkUpdateAssetStatusResponseDto } from './dto/bulk-update-asset-status-response.dto';
import {
  CreateAssetImageDto,
  UpdateAssetImagesSortOrderDto,
} from './dto/asset-image.dto';
import {
  assets,
  assetLocations,
  assetImages,
  AssetStatus,
  AssetType,
} from '../drizzle/schema/assets.schema';
import { MediaReferenceType } from '../drizzle/schema/media.schema';
import { media } from '../drizzle/schema/media.schema';
import { suppliers } from '../drizzle/schema/suppliers.schema';
import { assetCategories } from '../drizzle/schema/asset-categories.schema';
import {
  eq,
  and,
  or,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  inArray,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  EntityType,
  ActivityType,
  ExecutionStrategy,
  ActivitySource,
} from '../shared/types/activity.enum';
import { UsersService } from '../users/users.service';
import { MediaService } from '../media/media.service';
import { GcsUploadService } from '../gcs-upload/gcs-upload.service';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class AssetsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly usersService: UsersService,
    private readonly mediaService: MediaService,
    private readonly gcsUploadService: GcsUploadService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createAssetDto: CreateAssetDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if an asset with the same asset code already exists for this business
      const existingAsset = await this.db
        .select()
        .from(assets)
        .where(
          and(
            eq(assets.businessId, businessId),
            ilike(assets.assetCode, createAssetDto.assetCode),
            eq(assets.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingAsset) {
        throw new ConflictException(
          `Asset with code "${createAssetDto.assetCode}" already exists`,
        );
      }

      // Create the asset
      const [newAsset] = await this.db
        .insert(assets)
        .values({
          businessId,
          assetCode: createAssetDto.assetCode,
          name: createAssetDto.name,
          type: createAssetDto.type ?? AssetType.OTHER,
          supplierId: createAssetDto.supplierId,
          purchaseDate: createAssetDto.purchaseDate,
          purchasePrice: createAssetDto.purchasePrice,
          purchaseOrderNumber: createAssetDto.purchaseOrderNumber,
          maintenanceFrequencyValue: createAssetDto.maintenanceFrequencyValue,
          maintenanceFrequency: createAssetDto.maintenanceFrequency,
          lastMaintenanceDate: createAssetDto.lastMaintenanceDate,
          maintenanceDueDate: createAssetDto.maintenanceDueDate,
          warrantyExpiryDate: createAssetDto.warrantyExpiryDate,
          warrantyPeriod: createAssetDto.warrantyPeriod,
          lifecycleExpiryDate: createAssetDto.lifecycleExpiryDate,
          key1: createAssetDto.key1,
          key2: createAssetDto.key2,
          key3: createAssetDto.key3,
          key4: createAssetDto.key4,
          key5: createAssetDto.key5,
          key6: createAssetDto.key6,
          key7: createAssetDto.key7,
          key8: createAssetDto.key8,
          key9: createAssetDto.key9,
          key10: createAssetDto.key10,
          categoryId: createAssetDto.categoryId,
          subCategoryId: createAssetDto.subCategoryId,
          isAllocatedToAllLocations:
            createAssetDto.isAllocatedToAllLocations ?? false,
          bookValue: createAssetDto.bookValue,
          fixedAssetAccountId: createAssetDto.fixedAssetAccountId,
          depreciationAccountId: createAssetDto.depreciationAccountId,
          expenseAccountId: createAssetDto.expenseAccountId,
          description: createAssetDto.description,
          notes: createAssetDto.notes,
          reference: createAssetDto.reference,
          referenceId: createAssetDto.referenceId,
          status: (createAssetDto.status ?? AssetStatus.AVAILABLE) as any,
          createdBy: userId,
        })
        .returning();

      // Log the asset creation activity
      await this.activityLogService.logCreate(
        newAsset.id,
        EntityType.ASSET,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: newAsset.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create asset');
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    assetCode?: string,
    status?: string,
    typeId?: string,
    locationId?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: AssetDto[];
    meta: {
      total: number;
      page: number;
      totalPages: number;
    };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(assets.isDeleted, false),
      eq(assets.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(assets.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(assets.createdAt, toDate));
      }
    }

    // Add name filtering if provided
    if (name) {
      whereConditions.push(ilike(assets.name, `%${name}%`));
    }

    // Add asset code filtering if provided
    if (assetCode) {
      whereConditions.push(ilike(assets.assetCode, `%${assetCode}%`));
    }

    // Add status filtering if provided
    if (status) {
      const decodedStatus = decodeURIComponent(status);
      const statusArray = decodedStatus.split(',').map((s) => s.trim() as any);
      whereConditions.push(inArray(assets.status, statusArray));
    }

    // Add type filtering if provided
    if (typeId) {
      whereConditions.push(eq(assets.type, typeId as any));
    }

    // Build sort conditions
    let orderBy = [desc(assets.createdAt)];

    if (sort) {
      try {
        const parsedSort = JSON.parse(sort);
        if (parsedSort.length > 0) {
          const sortField = parsedSort[0];
          const isDesc = sortField.desc === true;

          switch (sortField.id) {
            case 'name':
              orderBy = [
                isDesc ? desc(assets.name) : asc(assets.name),
                asc(assets.id),
              ];
              break;
            case 'assetCode':
              orderBy = [
                isDesc ? desc(assets.assetCode) : asc(assets.assetCode),
                asc(assets.id),
              ];
              break;
            case 'status':
              orderBy = [
                isDesc ? desc(assets.status) : asc(assets.status),
                asc(assets.id),
              ];
              break;
            case 'createdAt':
              orderBy = [
                isDesc ? desc(assets.createdAt) : asc(assets.createdAt),
                asc(assets.id),
              ];
              break;
          }
        }
      } catch {
        // Invalid JSON, use default sort
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(assets)
      .where(and(...whereConditions));

    const total = Number(totalResult[0]?.count) || 0;
    const totalPages = Math.ceil(total / limit);

    // Get paginated results
    const result = await this.db
      .select()
      .from(assets)
      .where(and(...whereConditions))
      .orderBy(...orderBy)
      .limit(limit)
      .offset(offset);

    // Log the activity - skipped for performance reasons
    // Note: Skipping view activity logging for performance reasons

    const assetsData = await Promise.all(
      result.map((asset) => this.mapToAssetDto(asset)),
    );

    return {
      data: assetsData,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    assetCode?: string,
    status?: string,
    type?: string,
    categoryId?: string,
    subCategoryId?: string,
    locationId?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: AssetListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(assets.isDeleted, false),
      eq(assets.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(assets.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(assets.createdAt, toDate));
      }
    }

    // Add name filtering if provided (searches both name and assetCode)
    if (name) {
      whereConditions.push(
        or(
          ilike(assets.name, `%${name}%`),
          ilike(assets.assetCode, `%${name}%`),
        ),
      );
    }

    // Add assetCode filtering if provided
    if (assetCode) {
      whereConditions.push(ilike(assets.assetCode, `%${assetCode}%`));
    }

    // Add status filtering if provided
    if (status) {
      const decodedStatus = decodeURIComponent(status);
      const statusArray = decodedStatus
        .split(',')
        .map((s) => s.trim() as AssetStatus);
      whereConditions.push(inArray(assets.status, statusArray));
    }

    // Add type filtering if provided
    if (type) {
      const decodedType = decodeURIComponent(type);
      const typeArray = decodedType
        .split(',')
        .map((s) => s.trim() as AssetType);
      whereConditions.push(inArray(assets.type, typeArray));
    }

    // Add category filtering if provided
    if (categoryId) {
      const decodedCategoryId = decodeURIComponent(categoryId);
      const categoryIdArray = decodedCategoryId.split(',').map((s) => s.trim());
      whereConditions.push(inArray(assets.categoryId, categoryIdArray));
    }

    // Add sub-category filtering if provided
    if (subCategoryId) {
      const decodedSubCategoryId = decodeURIComponent(subCategoryId);
      const subCategoryIdArray = decodedSubCategoryId
        .split(',')
        .map((s) => s.trim());
      whereConditions.push(inArray(assets.subCategoryId, subCategoryIdArray));
    }

    // Add advanced filters if provided
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const filterConditions = [];

        for (const filter of parsedFilters) {
          const { id: fieldId, value, operator } = filter;

          if (fieldId === 'name') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(ilike(assets.name, `%${value}%`));
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(assets.name, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(assets.name, value));
                break;
              case 'ne':
                filterConditions.push(sql`${assets.name} != ${value}`);
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${assets.name} IS NULL OR ${assets.name} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${assets.name} IS NOT NULL AND ${assets.name} != ''`,
                );
                break;
            }
          } else if (fieldId === 'assetCode') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(ilike(assets.assetCode, `%${value}%`));
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(assets.assetCode, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(assets.assetCode, value));
                break;
              case 'ne':
                filterConditions.push(sql`${assets.assetCode} != ${value}`);
                break;
            }
          } else if (fieldId === 'status') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    inArray(assets.status, value as AssetStatus[]),
                  );
                } else {
                  filterConditions.push(eq(assets.status, value));
                }
                break;
              case 'ne':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    sql`${assets.status} NOT IN (${value.map((s) => `'${s}'`).join(',')})`,
                  );
                } else {
                  filterConditions.push(sql`${assets.status} != ${value}`);
                }
                break;
            }
          } else if (fieldId === 'type') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    inArray(assets.type, value as AssetType[]),
                  );
                } else {
                  filterConditions.push(eq(assets.type, value));
                }
                break;
              case 'ne':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    sql`${assets.type} NOT IN (${value.map((s) => `'${s}'`).join(',')})`,
                  );
                } else {
                  filterConditions.push(sql`${assets.type} != ${value}`);
                }
                break;
            }
          } else if (fieldId === 'categoryId') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  filterConditions.push(inArray(assets.categoryId, value));
                } else {
                  filterConditions.push(eq(assets.categoryId, value));
                }
                break;
              case 'ne':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    sql`${assets.categoryId} NOT IN (${value.map((s) => `'${s}'`).join(',')})`,
                  );
                } else {
                  filterConditions.push(sql`${assets.categoryId} != ${value}`);
                }
                break;
              case 'isEmpty':
                filterConditions.push(sql`${assets.categoryId} IS NULL`);
                break;
              case 'isNotEmpty':
                filterConditions.push(sql`${assets.categoryId} IS NOT NULL`);
                break;
            }
          } else if (fieldId === 'subCategoryId') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  filterConditions.push(inArray(assets.subCategoryId, value));
                } else {
                  filterConditions.push(eq(assets.subCategoryId, value));
                }
                break;
              case 'ne':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    sql`${assets.subCategoryId} NOT IN (${value.map((s) => `'${s}'`).join(',')})`,
                  );
                } else {
                  filterConditions.push(
                    sql`${assets.subCategoryId} != ${value}`,
                  );
                }
                break;
              case 'isEmpty':
                filterConditions.push(sql`${assets.subCategoryId} IS NULL`);
                break;
              case 'isNotEmpty':
                filterConditions.push(sql`${assets.subCategoryId} IS NOT NULL`);
                break;
            }
          }
        }

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            whereConditions.push(or(...filterConditions));
          } else {
            whereConditions.push(and(...filterConditions));
          }
        }
      } catch {
        // Invalid JSON, ignore filters
      }
    }

    // Build optimized sort conditions with proper indexing strategy
    let orderBy = [asc(assets.createdAt), asc(assets.id)];

    if (sort) {
      try {
        const parsedSort = JSON.parse(sort);
        if (parsedSort.length > 0) {
          const sortField = parsedSort[0];
          const isDesc = sortField.desc === true;

          switch (sortField.id) {
            case 'name':
              orderBy = [
                isDesc ? desc(assets.name) : asc(assets.name),
                asc(assets.id),
              ];
              break;
            case 'assetCode':
              orderBy = [
                isDesc ? desc(assets.assetCode) : asc(assets.assetCode),
                asc(assets.id),
              ];
              break;
            case 'createdAt':
              orderBy = [
                isDesc ? desc(assets.createdAt) : asc(assets.createdAt),
                asc(assets.id),
              ];
              break;
            case 'updatedAt':
              orderBy = [
                isDesc ? desc(assets.updatedAt) : asc(assets.updatedAt),
                asc(assets.id),
              ];
              break;
            case 'purchaseDate':
              orderBy = [
                isDesc ? desc(assets.purchaseDate) : asc(assets.purchaseDate),
                asc(assets.id),
              ];
              break;
          }
        }
      } catch {
        // Invalid JSON, use default sort
      }
    }

    // Execute optimized query with joins
    const result = await this.db
      .select({
        id: assets.id,
        assetCode: assets.assetCode,
        name: assets.name,
        type: assets.type,
        status: assets.status,
        categoryId: assets.categoryId,
        categoryName: sql<string>`category.name`.as('categoryName'),
        subCategoryId: assets.subCategoryId,
        subCategoryName: sql<string>`subcategory.name`.as('subCategoryName'),
        supplierId: assets.supplierId,
        supplierName: sql<string>`supplier.name`.as('supplierName'),
        purchaseDate: assets.purchaseDate,
        purchasePrice: assets.purchasePrice,
        bookValue: assets.bookValue,
        isAllocatedToAllLocations: assets.isAllocatedToAllLocations,
        description: assets.description,
        createdAt: assets.createdAt,
        updatedAt: assets.updatedAt,
      })
      .from(assets)
      .leftJoin(
        sql`${assetCategories} category`,
        sql`${assets.categoryId} = category.id`,
      )
      .leftJoin(
        sql`${assetCategories} subcategory`,
        sql`${assets.subCategoryId} = subcategory.id`,
      )
      .leftJoin(suppliers, eq(assets.supplierId, suppliers.id))
      .where(and(...whereConditions))
      .orderBy(...orderBy)
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(assets)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Get locations count for each asset
    const assetIds = result.map((asset) => asset.id);
    const locationsCountQuery = await this.db
      .select({
        assetId: assetLocations.assetId,
        count: sql<number>`count(*)`.as('count'),
      })
      .from(assetLocations)
      .where(inArray(assetLocations.assetId, assetIds))
      .groupBy(assetLocations.assetId);

    const locationsCountMap = new Map(
      locationsCountQuery.map((l) => [l.assetId, l.count]),
    );

    // Log the activity - skipped for performance reasons
    // Note: Skipping view activity logging for performance reasons

    // Build final data
    const data: AssetListDto[] = result.map((asset) => ({
      id: asset.id.toString(),
      assetCode: asset.assetCode,
      name: asset.name,
      type: asset.type,
      status: asset.status,
      categoryId: asset.categoryId?.toString(),
      categoryName: asset.categoryName || undefined,
      subCategoryId: asset.subCategoryId?.toString(),
      subCategoryName: asset.subCategoryName || undefined,
      supplierId: asset.supplierId?.toString(),
      supplierName: asset.supplierName || undefined,
      purchaseDate: asset.purchaseDate?.toString(),
      purchasePrice: asset.purchasePrice?.toString(),
      bookValue: asset.bookValue?.toString(),
      isAllocatedToAllLocations: asset.isAllocatedToAllLocations,
      locationsCount: locationsCountMap.get(asset.id) || 0,
      description: asset.description,
      createdAt: asset.createdAt,
      updatedAt: asset.updatedAt,
    }));

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<AssetDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const asset = await this.db
      .select()
      .from(assets)
      .where(
        and(
          eq(assets.id, id),
          eq(assets.businessId, businessId),
          eq(assets.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!asset) {
      throw new NotFoundException(`Asset with ID ${id} not found`);
    }

    // Log the activity
    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logCreate(...);

    return this.mapToAssetDto(asset);
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<AssetSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const assetResults = await this.db
      .select({
        id: assets.id,
        name: assets.name,
        assetCode: assets.assetCode,
      })
      .from(assets)
      .where(
        and(eq(assets.isDeleted, false), eq(assets.businessId, businessId)),
      )
      .orderBy(asc(assets.name), asc(assets.id));

    // Note: Skipping view activity logging for performance reasons

    return assetResults.map((asset) => ({
      id: asset.id,
      name: asset.name,
      assetCode: asset.assetCode,
    }));
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateAssetDto: UpdateAssetDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      return await this.db.transaction(async (tx) => {
        const existingAsset = await tx
          .select()
          .from(assets)
          .where(
            and(
              eq(assets.id, id),
              eq(assets.businessId, businessId),
              eq(assets.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!existingAsset) {
          throw new NotFoundException(`Asset with ID ${id} not found`);
        }

        // Check for asset code conflicts
        if (
          updateAssetDto.assetCode &&
          updateAssetDto.assetCode !== existingAsset.assetCode
        ) {
          const conflictingAsset = await tx
            .select()
            .from(assets)
            .where(
              and(
                eq(assets.businessId, businessId),
                ilike(assets.assetCode, updateAssetDto.assetCode),
                eq(assets.isDeleted, false),
              ),
            )
            .then((results) => results[0]);

          if (conflictingAsset) {
            throw new ConflictException(
              `Asset with code "${updateAssetDto.assetCode}" already exists`,
            );
          }
        }

        // Update the asset
        const [updatedAsset] = await tx
          .update(assets)
          .set({
            assetCode: updateAssetDto.assetCode ?? existingAsset.assetCode,
            name: updateAssetDto.name ?? existingAsset.name,
            type: updateAssetDto.type ?? existingAsset.type,
            supplierId: updateAssetDto.supplierId ?? existingAsset.supplierId,
            purchaseDate:
              updateAssetDto.purchaseDate ?? existingAsset.purchaseDate,
            purchasePrice:
              updateAssetDto.purchasePrice ?? existingAsset.purchasePrice,
            purchaseOrderNumber:
              updateAssetDto.purchaseOrderNumber ??
              existingAsset.purchaseOrderNumber,
            maintenanceFrequencyValue:
              updateAssetDto.maintenanceFrequencyValue ??
              existingAsset.maintenanceFrequencyValue,
            maintenanceFrequency:
              updateAssetDto.maintenanceFrequency ??
              existingAsset.maintenanceFrequency,
            lastMaintenanceDate:
              updateAssetDto.lastMaintenanceDate ??
              existingAsset.lastMaintenanceDate,
            maintenanceDueDate:
              updateAssetDto.maintenanceDueDate ??
              existingAsset.maintenanceDueDate,
            warrantyExpiryDate:
              updateAssetDto.warrantyExpiryDate ??
              existingAsset.warrantyExpiryDate,
            warrantyPeriod:
              updateAssetDto.warrantyPeriod ?? existingAsset.warrantyPeriod,
            lifecycleExpiryDate:
              updateAssetDto.lifecycleExpiryDate ??
              existingAsset.lifecycleExpiryDate,
            key1: updateAssetDto.key1 ?? existingAsset.key1,
            key2: updateAssetDto.key2 ?? existingAsset.key2,
            key3: updateAssetDto.key3 ?? existingAsset.key3,
            key4: updateAssetDto.key4 ?? existingAsset.key4,
            key5: updateAssetDto.key5 ?? existingAsset.key5,
            key6: updateAssetDto.key6 ?? existingAsset.key6,
            key7: updateAssetDto.key7 ?? existingAsset.key7,
            key8: updateAssetDto.key8 ?? existingAsset.key8,
            key9: updateAssetDto.key9 ?? existingAsset.key9,
            key10: updateAssetDto.key10 ?? existingAsset.key10,
            categoryId: updateAssetDto.categoryId ?? existingAsset.categoryId,
            subCategoryId:
              updateAssetDto.subCategoryId ?? existingAsset.subCategoryId,
            isAllocatedToAllLocations:
              updateAssetDto.isAllocatedToAllLocations ??
              existingAsset.isAllocatedToAllLocations,
            bookValue: updateAssetDto.bookValue ?? existingAsset.bookValue,
            fixedAssetAccountId:
              updateAssetDto.fixedAssetAccountId ??
              existingAsset.fixedAssetAccountId,
            depreciationAccountId:
              updateAssetDto.depreciationAccountId ??
              existingAsset.depreciationAccountId,
            expenseAccountId:
              updateAssetDto.expenseAccountId ?? existingAsset.expenseAccountId,
            description:
              updateAssetDto.description ?? existingAsset.description,
            notes: updateAssetDto.notes ?? existingAsset.notes,
            reference: updateAssetDto.reference ?? existingAsset.reference,
            referenceId:
              updateAssetDto.referenceId ?? existingAsset.referenceId,
            status: updateAssetDto.status ?? existingAsset.status,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(assets.id, id))
          .returning();

        // Handle image management
        await this.handleImageManagement(
          tx,
          userId,
          businessId,
          id,
          updateAssetDto,
        );

        // Handle attachment management
        await this.handleAttachmentManagement(
          tx,
          userId,
          businessId,
          id,
          updateAssetDto,
        );

        await this.activityLogService.logUpdate(
          updatedAsset.id,
          EntityType.ASSET,
          userId,
          businessId,
          {
            source: metadata?.source || ActivitySource.WEB,
            ipAddress: metadata?.ipAddress,
            userAgent: metadata?.userAgent,
            sessionId: metadata?.sessionId,
          },
        );

        return {
          id: updatedAsset.id,
        };
      });
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to update asset');
    }
  }

  /**
   * Handle image management operations during asset update
   */
  private async handleImageManagement(
    tx: any,
    userId: string,
    businessId: string,
    assetId: string,
    updateAssetDto: UpdateAssetDto,
  ): Promise<void> {
    const { keepImageIds, removeImageIds, imageUpdates } = updateAssetDto;

    // Handle explicit removal of images
    if (removeImageIds && removeImageIds.length > 0) {
      // Remove from assetImages table
      await tx
        .delete(assetImages)
        .where(
          and(
            eq(assetImages.assetId, assetId),
            inArray(assetImages.imageId, removeImageIds),
          ),
        );

      // Soft delete from media table
      await tx
        .update(media)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(
          and(
            inArray(media.id, removeImageIds),
            eq(media.referenceId, assetId),
            eq(media.referenceType, MediaReferenceType.ASSETS),
            eq(media.businessId, businessId),
          ),
        );
    }

    // Handle keeping specific images (remove all others)
    if (keepImageIds && keepImageIds.length > 0) {
      // Get all current image IDs for this asset
      const currentImages = await tx
        .select({ imageId: assetImages.imageId })
        .from(assetImages)
        .where(eq(assetImages.assetId, assetId));

      const currentImageIds = currentImages.map(
        (img: { imageId: string }) => img.imageId,
      );
      const imagesToRemove = currentImageIds.filter(
        (id: string) => !keepImageIds.includes(id),
      );

      if (imagesToRemove.length > 0) {
        // Remove from assetImages table
        await tx
          .delete(assetImages)
          .where(
            and(
              eq(assetImages.assetId, assetId),
              inArray(assetImages.imageId, imagesToRemove),
            ),
          );

        // Soft delete from media table
        await tx
          .update(media)
          .set({
            isDeleted: true,
            updatedBy: userId,
          })
          .where(
            and(
              inArray(media.id, imagesToRemove),
              eq(media.referenceId, assetId),
              eq(media.referenceType, MediaReferenceType.ASSETS),
              eq(media.businessId, businessId),
            ),
          );
      }
    }

    // Handle image updates (sort order, primary status, etc.)
    if (imageUpdates && imageUpdates.length > 0) {
      for (const imageUpdate of imageUpdates) {
        const updateData: any = {
          updatedBy: userId,
          updatedAt: new Date(),
        };

        if (imageUpdate.sortOrder !== undefined) {
          updateData.sortOrder = imageUpdate.sortOrder;
        }
        if (imageUpdate.isPrimary !== undefined) {
          updateData.isPrimary = imageUpdate.isPrimary;
        }
        if (imageUpdate.isActive !== undefined) {
          updateData.isActive = imageUpdate.isActive;
        }

        await tx
          .update(assetImages)
          .set(updateData)
          .where(
            and(
              eq(assetImages.assetId, assetId),
              eq(assetImages.imageId, imageUpdate.imageId),
            ),
          );
      }
    }
  }

  /**
   * Handle attachment management operations during asset update
   */
  private async handleAttachmentManagement(
    tx: any,
    userId: string,
    businessId: string,
    assetId: string,
    updateAssetDto: UpdateAssetDto,
  ): Promise<void> {
    const { keepAttachmentIds, removeAttachmentIds, attachmentUpdates } =
      updateAssetDto;

    // Handle explicit removal of attachments
    if (removeAttachmentIds && removeAttachmentIds.length > 0) {
      // Soft delete from media table
      await tx
        .update(media)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(
          and(
            inArray(media.id, removeAttachmentIds),
            eq(media.referenceId, assetId),
            eq(media.referenceType, MediaReferenceType.ASSETS),
            eq(media.businessId, businessId),
            // Exclude images that are managed through assetImages table
            sql`${media.id} NOT IN (SELECT image_id FROM asset_images WHERE asset_id = ${assetId})`,
          ),
        );
    }

    // Handle keeping specific attachments (remove all others)
    if (keepAttachmentIds && keepAttachmentIds.length > 0) {
      // Get all current attachment IDs for this asset (excluding images)
      const currentAttachments = await tx
        .select({ id: media.id })
        .from(media)
        .where(
          and(
            eq(media.referenceId, assetId),
            eq(media.referenceType, MediaReferenceType.ASSETS),
            eq(media.businessId, businessId),
            eq(media.isDeleted, false),
            // Exclude images that are managed through assetImages table
            sql`${media.id} NOT IN (SELECT image_id FROM asset_images WHERE asset_id = ${assetId})`,
          ),
        );

      const currentAttachmentIds = currentAttachments.map(
        (att: { id: string }) => att.id,
      );
      const attachmentsToRemove = currentAttachmentIds.filter(
        (id: string) => !keepAttachmentIds.includes(id),
      );

      if (attachmentsToRemove.length > 0) {
        // Soft delete from media table
        await tx
          .update(media)
          .set({
            isDeleted: true,
            updatedBy: userId,
          })
          .where(
            and(
              inArray(media.id, attachmentsToRemove),
              eq(media.referenceId, assetId),
              eq(media.referenceType, MediaReferenceType.ASSETS),
              eq(media.businessId, businessId),
            ),
          );
      }
    }

    // Handle attachment updates (metadata updates)
    if (attachmentUpdates && attachmentUpdates.length > 0) {
      for (const attachmentUpdate of attachmentUpdates) {
        const updateData: any = {
          updatedBy: userId,
          updatedAt: new Date(),
        };

        // Note: For attachments, we primarily update metadata in the media table
        // Additional metadata can be stored in the media table's metadata field if needed
        await tx
          .update(media)
          .set(updateData)
          .where(
            and(
              eq(media.id, attachmentUpdate.attachmentId),
              eq(media.referenceId, assetId),
              eq(media.referenceType, MediaReferenceType.ASSETS),
              eq(media.businessId, businessId),
            ),
          );
      }
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const asset = await this.db
      .select()
      .from(assets)
      .where(
        and(
          eq(assets.id, id),
          eq(assets.businessId, businessId),
          eq(assets.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!asset) {
      throw new NotFoundException(`Asset with ID ${id} not found`);
    }

    await this.db
      .update(assets)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(assets.id, id));

    await this.activityLogService.logDelete(
      asset.id,
      EntityType.ASSET,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      id: asset.id,
      message: 'Asset deleted successfully',
    };
  }

  private async mapToAssetDto(asset: any): Promise<AssetDto> {
    const createdBy = await this.usersService.getUserName(asset.createdBy);

    let updatedBy: string | undefined;
    if (asset.updatedBy) {
      updatedBy = await this.usersService.getUserName(asset.updatedBy);
    }

    return {
      id: asset.id,
      businessId: asset.businessId,
      assetCode: asset.assetCode,
      name: asset.name,
      type: asset.type,
      supplierId: asset.supplierId,
      purchaseDate: asset.purchaseDate?.toISOString().split('T')[0],
      purchasePrice: asset.purchasePrice,
      purchaseOrderNumber: asset.purchaseOrderNumber,
      maintenanceFrequencyValue: asset.maintenanceFrequencyValue,
      maintenanceFrequency: asset.maintenanceFrequency,
      lastMaintenanceDate: asset.lastMaintenanceDate
        ?.toISOString()
        .split('T')[0],
      maintenanceDueDate: asset.maintenanceDueDate?.toISOString().split('T')[0],
      warrantyExpiryDate: asset.warrantyExpiryDate?.toISOString().split('T')[0],
      warrantyPeriod: asset.warrantyPeriod,
      lifecycleExpiryDate: asset.lifecycleExpiryDate
        ?.toISOString()
        .split('T')[0],
      key1: asset.key1,
      key2: asset.key2,
      key3: asset.key3,
      key4: asset.key4,
      key5: asset.key5,
      key6: asset.key6,
      key7: asset.key7,
      key8: asset.key8,
      key9: asset.key9,
      key10: asset.key10,
      categoryId: asset.categoryId,
      subCategoryId: asset.subCategoryId,
      isAllocatedToAllLocations: asset.isAllocatedToAllLocations,
      bookValue: asset.bookValue,
      fixedAssetAccountId: asset.fixedAssetAccountId,
      depreciationAccountId: asset.depreciationAccountId,
      expenseAccountId: asset.expenseAccountId,
      description: asset.description,
      notes: asset.notes,
      reference: asset.reference,
      referenceId: asset.referenceId,
      status: asset.status,
      createdBy,
      updatedBy,
      createdAt: asset.createdAt,
      updatedAt: asset.updatedAt,
    };
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    assetData: CreateAssetDto[],
    metadata?: ActivityMetadata,
  ): Promise<BulkAssetIdsResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const createdIds: string[] = [];

    try {
      for (const assetDto of assetData) {
        const result = await this.create(
          userId,
          businessId,
          assetDto,
          metadata,
        );
        createdIds.push(result.id);
      }

      // Log bulk create operation
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_CREATE,
        EntityType.ASSET,
        createdIds,
        {
          names: assetData.map((dto) => dto.name),
          status: 'created',
        },
        userId,
        businessId,
        {
          filterCriteria: { count: assetData.length },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { ids: createdIds };
    } catch (error) {
      throw new BadRequestException(
        `Failed to create assets: ${error.message}`,
      );
    }
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    assetIds: string[],
    metadata?: ActivityMetadata,
  ): Promise<BulkDeleteAssetResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!assetIds || assetIds.length === 0) {
      throw new BadRequestException('Asset IDs are required');
    }

    try {
      const now = new Date();
      const deletedAssets = await this.db
        .update(assets)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: now,
        })
        .where(
          and(
            eq(assets.businessId, businessId),
            inArray(assets.id, assetIds),
            eq(assets.isDeleted, false),
          ),
        )
        .returning({ id: assets.id });

      const deletedIds = deletedAssets.map((asset) => asset.id);

      // Log bulk delete operation
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_DELETE,
        EntityType.ASSET,
        deletedIds,
        { isDeleted: true, updatedBy: userId },
        userId,
        businessId,
        {
          filterCriteria: { assetIds },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        deletedCount: deletedIds.length,
        deletedIds,
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to delete assets: ${error.message}`,
      );
    }
  }

  async bulkUpdateStatus(
    userId: string,
    businessId: string | null,
    assetIds: string[],
    status: AssetStatus,
    metadata?: ActivityMetadata,
  ): Promise<BulkUpdateAssetStatusResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!assetIds || assetIds.length === 0) {
      throw new BadRequestException('Asset IDs are required');
    }

    try {
      const now = new Date();
      const updatedAssets = await this.db
        .update(assets)
        .set({
          status,
          updatedBy: userId,
          updatedAt: now,
        })
        .where(
          and(
            eq(assets.businessId, businessId),
            inArray(assets.id, assetIds),
            eq(assets.isDeleted, false),
          ),
        )
        .returning({ id: assets.id });

      const updatedIds = updatedAssets.map((asset) => asset.id);

      // Log bulk status update operation
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_STATUS_CHANGE,
        EntityType.ASSET,
        updatedIds,
        { status },
        userId,
        businessId,
        {
          filterCriteria: { assetIds, targetStatus: status },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        updated: updatedIds.length,
        updatedIds,
        message: `Successfully updated ${updatedIds.length} assets`,
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to update asset status: ${error.message}`,
      );
    }
  }

  /**
   * Add images to an asset
   * @param userId - The user ID performing the action
   * @param businessId - The business ID
   * @param assetId - The asset ID
   * @param images - Array of image DTOs to add
   */
  async addAssetImages(
    userId: string,
    businessId: string | null,
    assetId: string,
    images: CreateAssetImageDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    // Validate asset exists and belongs to business
    const asset = await this.db
      .select({ id: assets.id })
      .from(assets)
      .where(
        and(
          eq(assets.id, assetId),
          eq(assets.businessId, businessId),
          eq(assets.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!asset) {
      throw new NotFoundException('Asset not found');
    }

    return await this.db.transaction(async (tx) => {
      const results = [];

      for (const image of images) {
        // If this is marked as primary, unset other primary images for the same asset
        if (image.isPrimary) {
          await tx
            .update(assetImages)
            .set({ isPrimary: false, updatedAt: new Date(), updatedBy: userId })
            .where(eq(assetImages.assetId, assetId));
        }

        const newImage = await tx
          .insert(assetImages)
          .values({
            assetId,
            imageId: image.imageId,
            sortOrder: image.sortOrder || 0,
            isPrimary: image.isPrimary || false,
            isActive: image.isActive ?? true,
            createdBy: userId,
            updatedBy: userId,
          })
          .returning({ id: assetImages.id });

        results.push(newImage[0]);
      }

      return results;
    });
  }

  /**
   * Update asset images sort order
   * @param userId - The user ID performing the action
   * @param businessId - The business ID
   * @param assetId - The asset ID
   * @param updates - Sort order updates
   */
  async updateAssetImagesSortOrder(
    userId: string,
    businessId: string | null,
    assetId: string,
    updates: UpdateAssetImagesSortOrderDto,
    metadata?: ActivityMetadata,
  ): Promise<{ updated: number }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    // Validate asset exists and belongs to business
    const asset = await this.db
      .select({ id: assets.id })
      .from(assets)
      .where(
        and(
          eq(assets.id, assetId),
          eq(assets.businessId, businessId),
          eq(assets.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!asset) {
      throw new NotFoundException('Asset not found');
    }

    return await this.db.transaction(async (tx) => {
      let updated = 0;

      for (const imageUpdate of updates.images) {
        const result = await tx
          .update(assetImages)
          .set({
            sortOrder: imageUpdate.sortOrder,
            updatedAt: new Date(),
            updatedBy: userId,
          })
          .where(
            and(
              eq(assetImages.id, imageUpdate.id),
              eq(assetImages.assetId, assetId),
            ),
          )
          .returning({ id: assetImages.id });

        if (result.length > 0) {
          updated++;
        }
      }

      return { updated };
    });
  }

  /**
   * Remove an image from an asset
   * @param _userId - The user ID performing the action (unused)
   * @param businessId - The business ID
   * @param assetId - The asset ID
   * @param imageId - The image ID to remove
   */
  async removeAssetImage(
    _userId: string,
    businessId: string | null,
    assetId: string,
    imageId: string,
    metadata?: ActivityMetadata,
  ): Promise<{ deleted: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    // Validate asset exists and belongs to business
    const asset = await this.db
      .select({ id: assets.id })
      .from(assets)
      .where(
        and(
          eq(assets.id, assetId),
          eq(assets.businessId, businessId),
          eq(assets.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!asset) {
      throw new NotFoundException('Asset not found');
    }

    const deletedImages = await this.db
      .delete(assetImages)
      .where(and(eq(assetImages.id, imageId), eq(assetImages.assetId, assetId)))
      .returning({ id: assetImages.id });

    return { deleted: deletedImages.length > 0 };
  }

  /**
   * Set an image as the primary image for an asset
   * @param userId - The user ID performing the action
   * @param businessId - The business ID
   * @param assetId - The asset ID
   * @param imageId - The image ID to set as primary
   */
  async setAssetPrimaryImage(
    userId: string,
    businessId: string | null,
    assetId: string,
    imageId: string,
    metadata?: ActivityMetadata,
  ): Promise<{ updated: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    // Validate asset exists and belongs to business
    const asset = await this.db
      .select({ id: assets.id })
      .from(assets)
      .where(
        and(
          eq(assets.id, assetId),
          eq(assets.businessId, businessId),
          eq(assets.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!asset) {
      throw new NotFoundException('Asset not found');
    }

    // Validate the image exists for this asset
    const imageExists = await this.db
      .select({ id: assetImages.id })
      .from(assetImages)
      .where(and(eq(assetImages.id, imageId), eq(assetImages.assetId, assetId)))
      .then((results) => results[0]);

    if (!imageExists) {
      throw new NotFoundException('Asset image not found');
    }

    return await this.db.transaction(async (tx) => {
      // First, unset all primary images for this asset
      await tx
        .update(assetImages)
        .set({ isPrimary: false, updatedAt: new Date(), updatedBy: userId })
        .where(eq(assetImages.assetId, assetId));

      // Then set this image as primary
      const updatedImages = await tx
        .update(assetImages)
        .set({ isPrimary: true, updatedAt: new Date(), updatedBy: userId })
        .where(
          and(eq(assetImages.id, imageId), eq(assetImages.assetId, assetId)),
        )
        .returning({ id: assetImages.id });

      return { updated: updatedImages.length > 0 };
    });
  }

  async checkAssetCodeAvailability(
    userId: string,
    businessId: string | null,
    assetCode: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if an asset with the same asset code already exists for this business
    // Using ilike for case-insensitive comparison
    const existingAsset = await this.db
      .select()
      .from(assets)
      .where(
        and(
          eq(assets.businessId, businessId),
          ilike(assets.assetCode, assetCode),
          eq(assets.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingAsset };
  }

  /**
   * Create asset with file support (images and attachments)
   * @param userId - The user ID performing the action
   * @param businessId - The business ID
   * @param createAssetDto - Asset creation data
   * @param imageFiles - Image files to upload
   * @param attachmentFiles - Attachment files to upload
   */
  async createWithFiles(
    userId: string,
    businessId: string | null,
    createAssetDto: CreateAssetDto,
    imageFiles?: Express.Multer.File[],
    attachmentFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<AssetIdResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    return await this.db.transaction(async (tx) => {
      // First create the asset using existing logic
      const result = await this.create(
        userId,
        businessId,
        createAssetDto,
        metadata,
      );
      const assetId = result.id;

      // Handle multiple image uploads with asset reference
      if (imageFiles && imageFiles.length > 0) {
        for (let i = 0; i < imageFiles.length; i++) {
          const imageFile = imageFiles[i];
          const mediaResult = await this.mediaService.uploadMediaWithReference(
            imageFile,
            MediaReferenceType.ASSETS,
            businessId,
            userId,
            assetId,
          );

          // Add image to assetImages table with sort order
          await tx.insert(assetImages).values({
            assetId,
            imageId: mediaResult.id,
            sortOrder: i,
            isPrimary: i === 0, // First image is primary
            isActive: true,
            createdBy: userId,
            updatedBy: userId,
          });
        }
      }

      // Handle attachment uploads (stored directly in media table)
      if (attachmentFiles && attachmentFiles.length > 0) {
        for (const attachmentFile of attachmentFiles) {
          await this.mediaService.uploadMediaWithReference(
            attachmentFile,
            MediaReferenceType.ASSETS,
            businessId,
            userId,
            assetId,
          );
        }
      }

      return result;
    });
  }

  /**
   * Update asset with file support (images and attachments)
   * @param userId - The user ID performing the action
   * @param businessId - The business ID
   * @param id - Asset ID
   * @param updateAssetDto - Asset update data
   * @param imageFiles - New image files to upload
   * @param attachmentFiles - New attachment files to upload
   */
  async updateWithFiles(
    userId: string,
    businessId: string | null,
    id: string,
    updateAssetDto: UpdateAssetDto,
    imageFiles?: Express.Multer.File[],
    attachmentFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<AssetIdResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    return await this.db.transaction(async (tx) => {
      // First update the asset using existing logic
      const result = await this.update(
        userId,
        businessId,
        id,
        updateAssetDto,
        metadata,
      );

      // Handle new image uploads if provided
      if (imageFiles && imageFiles.length > 0) {
        // Get current highest sort order for this asset
        const maxSortOrder = await tx
          .select({
            maxSort: sql<number>`COALESCE(MAX(${assetImages.sortOrder}), -1)`,
          })
          .from(assetImages)
          .where(eq(assetImages.assetId, id))
          .then((results) => results[0]?.maxSort || -1);

        for (let i = 0; i < imageFiles.length; i++) {
          const imageFile = imageFiles[i];
          const mediaResult = await this.mediaService.uploadMediaWithReference(
            imageFile,
            MediaReferenceType.ASSETS,
            businessId,
            userId,
            id,
          );

          // Add new image to assetImages table with incremental sort order
          await tx.insert(assetImages).values({
            assetId: id,
            imageId: mediaResult.id,
            sortOrder: maxSortOrder + 1 + i,
            isPrimary: false, // New images are not primary by default
            isActive: true,
            createdBy: userId,
            updatedBy: userId,
          });
        }
      }

      // Handle new attachment uploads
      if (attachmentFiles && attachmentFiles.length > 0) {
        for (const attachmentFile of attachmentFiles) {
          await this.mediaService.uploadMediaWithReference(
            attachmentFile,
            MediaReferenceType.ASSETS,
            businessId,
            userId,
            id,
          );
        }
      }

      return result;
    });
  }

  /**
   * Get all attachments for an asset
   * @param userId - The user ID performing the action
   * @param businessId - The business ID
   * @param assetId - The asset ID
   */
  async getAssetAttachments(
    _userId: string,
    businessId: string | null,
    assetId: string,
  ) {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    // Validate asset exists and belongs to business
    const asset = await this.db
      .select({ id: assets.id })
      .from(assets)
      .where(
        and(
          eq(assets.id, assetId),
          eq(assets.businessId, businessId),
          eq(assets.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!asset) {
      throw new NotFoundException('Asset not found');
    }

    // Get all media files (attachments) for this asset
    // Exclude images that are in the assetImages table
    const attachments = await this.db
      .select({
        id: media.id,
        fileName: media.fileName,
        originalName: media.originalName,
        publicUrl: media.publicUrl,
        size: media.size,
        mimeType: media.mimeType,
        mediaType: media.mediaType,
        uploadedAt: media.uploadedAt,
      })
      .from(media)
      .where(
        and(
          eq(media.referenceId, assetId),
          eq(media.referenceType, MediaReferenceType.ASSETS),
          eq(media.businessId, businessId),
          eq(media.isDeleted, false),
          // Exclude images that are managed through assetImages table
          sql`${media.id} NOT IN (SELECT image_id FROM asset_images WHERE asset_id = ${assetId})`,
        ),
      )
      .orderBy(media.uploadedAt);

    return { attachments };
  }

  /**
   * Add attachments to an asset
   * @param userId - The user ID performing the action
   * @param businessId - The business ID
   * @param assetId - The asset ID
   * @param attachmentFiles - Attachment files to upload
   */
  async addAssetAttachments(
    userId: string,
    businessId: string | null,
    assetId: string,
    attachmentFiles: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ) {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    // Validate asset exists and belongs to business
    const asset = await this.db
      .select({ id: assets.id })
      .from(assets)
      .where(
        and(
          eq(assets.id, assetId),
          eq(assets.businessId, businessId),
          eq(assets.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!asset) {
      throw new NotFoundException('Asset not found');
    }

    const uploadedAttachments = [];

    for (const attachmentFile of attachmentFiles) {
      const mediaResult = await this.mediaService.uploadMediaWithReference(
        attachmentFile,
        MediaReferenceType.ASSETS,
        businessId,
        userId,
        assetId,
      );

      uploadedAttachments.push({
        id: mediaResult.id,
        fileName: mediaResult.fileName,
        originalName: mediaResult.originalName,
      });
    }

    return { attachments: uploadedAttachments };
  }

  /**
   * Remove an attachment from an asset
   * @param userId - The user ID performing the action
   * @param businessId - The business ID
   * @param assetId - The asset ID
   * @param attachmentId - The attachment ID to remove
   */
  async removeAssetAttachment(
    _userId: string,
    businessId: string | null,
    assetId: string,
    attachmentId: string,
    metadata?: ActivityMetadata,
  ): Promise<{ deleted: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    // Validate asset exists and belongs to business
    const asset = await this.db
      .select({ id: assets.id })
      .from(assets)
      .where(
        and(
          eq(assets.id, assetId),
          eq(assets.businessId, businessId),
          eq(assets.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!asset) {
      throw new NotFoundException('Asset not found');
    }

    // Validate the attachment exists for this asset and is not an image
    const attachment = await this.db
      .select({ id: media.id })
      .from(media)
      .where(
        and(
          eq(media.id, attachmentId),
          eq(media.referenceId, assetId),
          eq(media.referenceType, MediaReferenceType.ASSETS),
          eq(media.businessId, businessId),
          eq(media.isDeleted, false),
          // Ensure it's not an image managed through assetImages table
          sql`${media.id} NOT IN (SELECT image_id FROM asset_images WHERE asset_id = ${assetId})`,
        ),
      )
      .then((results) => results[0]);

    if (!attachment) {
      throw new NotFoundException('Attachment not found');
    }

    try {
      // Delete from media service (cloud storage + database)
      await this.mediaService.deleteMedia(
        attachmentId,
        businessId,
        MediaReferenceType.ASSETS,
      );

      return { deleted: true };
    } catch (error) {
      throw new BadRequestException(
        `Failed to delete attachment: ${error.message}`,
      );
    }
  }
}

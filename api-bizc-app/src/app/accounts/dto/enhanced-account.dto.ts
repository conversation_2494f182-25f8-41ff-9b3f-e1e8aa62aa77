import {
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Optional,
  IsUUID,
  IsDateString,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class AccountBalanceDto {
  @ApiProperty({ description: 'Account ID' })
  @IsUUID()
  accountId: string;

  @ApiProperty({ description: 'Total debit amount' })
  @IsNumber()
  @Type(() => Number)
  debitBalance: number;

  @ApiProperty({ description: 'Total credit amount' })
  @IsNumber()
  @Type(() => Number)
  creditBalance: number;

  @ApiProperty({ description: 'Net balance (debits - credits)' })
  @IsNumber()
  @Type(() => Number)
  netBalance: number;

  @ApiProperty({ description: 'Balance as of date' })
  @IsDateString()
  asOfDate: string;
}

export class AccountBalanceQueryDto {
  @ApiPropertyOptional({
    description: 'As of date for balance calculation (YYYY-MM-DD)',
  })
  @IsOptional()
  @IsDateString()
  asOfDate?: string;
}

export class AccountHistoryQueryDto {
  @ApiProperty({ description: 'Start date for history (YYYY-MM-DD)' })
  @IsDateString()
  startDate: string;

  @ApiProperty({ description: 'End date for history (YYYY-MM-DD)' })
  @IsDateString()
  endDate: string;

  @ApiPropertyOptional({
    description: 'Number of transactions to return',
    default: 100,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  limit?: number;
}

export class CreateJournalEntryLineDto {
  @ApiProperty({ description: 'Account ID' })
  @IsUUID()
  accountId: string;

  @ApiProperty({ description: 'Debit amount' })
  @IsNumber()
  @Type(() => Number)
  debitAmount: number;

  @ApiProperty({ description: 'Credit amount' })
  @IsNumber()
  @Type(() => Number)
  creditAmount: number;

  @ApiPropertyOptional({ description: 'Line description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Entity type',
    enum: ['customer', 'supplier', 'employee'],
  })
  @IsOptional()
  @IsString()
  entityType?: string;

  @ApiPropertyOptional({ description: 'Entity ID' })
  @IsOptional()
  @IsString()
  entityId?: string;

  @ApiPropertyOptional({ description: 'Tax ID' })
  @IsOptional()
  @IsUUID()
  taxId?: string;
}

export class CreateJournalEntryDto {
  @ApiProperty({ description: 'Journal entry date (YYYY-MM-DD)' })
  @IsDateString()
  journalDate: string;

  @ApiProperty({ description: 'Entry description' })
  @IsString()
  description: string;

  @ApiPropertyOptional({ description: 'Additional memo' })
  @IsOptional()
  @IsString()
  memo?: string;

  @ApiProperty({
    description: 'Reference type',
    enum: ['MANUAL', 'INVOICE', 'BILL', 'PAYMENT', 'ADJUSTMENT'],
  })
  @IsString()
  referenceType: string;

  @ApiPropertyOptional({ description: 'Reference ID if applicable' })
  @IsOptional()
  @IsString()
  referenceId?: string;

  @ApiProperty({
    description: 'Journal entry lines',
    type: [CreateJournalEntryLineDto],
  })
  @Type(() => CreateJournalEntryLineDto)
  lines: CreateJournalEntryLineDto[];
}

export class PostJournalEntryDto {
  @ApiPropertyOptional({ description: 'Reason for posting' })
  @IsOptional()
  @IsString()
  reason?: string;
}

export class VoidJournalEntryDto {
  @ApiProperty({ description: 'Reason for voiding' })
  @IsString()
  reason: string;
}

export class JournalEntryFilterDto {
  @ApiPropertyOptional({
    description: 'Filter by status',
    enum: ['draft', 'posted', 'voided'],
  })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiPropertyOptional({ description: 'Start date for filtering (YYYY-MM-DD)' })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({ description: 'End date for filtering (YYYY-MM-DD)' })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({ description: 'Filter by account ID' })
  @IsOptional()
  @IsUUID()
  accountId?: string;

  @ApiPropertyOptional({
    description: 'Number of entries to return',
    default: 50,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  limit?: number;

  @ApiPropertyOptional({ description: 'Offset for pagination', default: 0 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  offset?: number;
}

export class TrialBalanceDto {
  @ApiProperty({ description: 'Report as of date' })
  @IsDateString()
  asOfDate: string;

  @ApiProperty({ description: 'Account details with balances' })
  accounts: TrialBalanceAccountDto[];

  @ApiProperty({ description: 'Total debits' })
  @IsNumber()
  totalDebits: number;

  @ApiProperty({ description: 'Total credits' })
  @IsNumber()
  totalCredits: number;

  @ApiProperty({ description: 'Whether trial balance is balanced' })
  isBalanced: boolean;
}

export class TrialBalanceAccountDto {
  @ApiProperty({ description: 'Account ID' })
  @IsUUID()
  id: string;

  @ApiProperty({ description: 'Account number' })
  @IsString()
  accountNumber: string;

  @ApiProperty({ description: 'Account name' })
  @IsString()
  accountName: string;

  @ApiProperty({ description: 'Account category' })
  @IsString()
  accountCategory: string;

  @ApiProperty({ description: 'Account type' })
  @IsString()
  accountType: string;

  @ApiProperty({ description: 'Debit balance' })
  @IsNumber()
  debitBalance: number;

  @ApiProperty({ description: 'Credit balance' })
  @IsNumber()
  creditBalance: number;

  @ApiProperty({ description: 'Net balance' })
  @IsNumber()
  netBalance: number;
}

import {
  Injectable,
  ConflictException,
  NotFoundException,
  UnauthorizedException,
  BadRequestException,
  Inject,
} from '@nestjs/common';
import {
  and,
  eq,
  ilike,
  isNull,
  isNotNull,
  asc,
  desc,
  or,
  sql,
  lte,
  gte,
  sum,
  count,
  inArray,
  like,
} from 'drizzle-orm';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { accounts } from '../drizzle/schema/accounts.schema';
import { users } from '../drizzle/schema/users.schema';
import { taxes } from '../drizzle/schema/taxes.schema';
import {
  journalEntries,
  journalEntryLines,
  JournalEntryStatus,
} from '../drizzle/schema/journal-entries.schema';
import { CreateAccountDto } from './dto/create-account.dto';
import { UpdateAccountDto } from './dto/update-account.dto';
import { AccountDto } from './dto/account.dto';
import { AccountListDto } from './dto/account-list.dto';
import { AccountSlimDto } from './dto/account-slim.dto';
import {
  AccountCategory,
  ChartAccountType,
  AccountDetailType,
} from '../shared/types';
import {
  EntityType,
  ActivityType,
  ActivitySource,
} from '../shared/types/activity.enum';
import { AccountStatus } from '../drizzle/schema/accounts.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';

// Journal Entry DTOs
import { CreateJournalEntryDto } from '../journal-entries/dto/create-journal-entry.dto';
import { UpdateJournalEntryDto } from '../journal-entries/dto/update-journal-entry.dto';
import { JournalEntryDto } from '../journal-entries/dto/journal-entry.dto';
import { JournalEntryIdResponseDto } from '../journal-entries/dto/journal-entry-id-response.dto';
import { DeleteJournalEntryResponseDto } from '../journal-entries/dto/delete-journal-entry-response.dto';

// Balance-related interfaces
export interface AccountBalance {
  accountId: string;
  debitBalance: number;
  creditBalance: number;
  netBalance: number;
  asOfDate: Date;
}

export interface TrialBalanceAccount {
  id: string;
  accountNumber: string;
  accountName: string;
  accountCategory: AccountCategory;
  accountType: ChartAccountType;
  debitBalance: number;
  creditBalance: number;
  netBalance: number;
}

export interface TrialBalanceReport {
  asOfDate: Date;
  accounts: TrialBalanceAccount[];
  totalDebits: number;
  totalCredits: number;
  isBalanced: boolean;
}

export interface BalanceSheetReport {
  asOfDate: Date;
  assets: {
    current: any[];
    nonCurrent: any[];
    total: number;
  };
  liabilities: {
    current: any[];
    nonCurrent: any[];
    total: number;
  };
  equity: {
    accounts: any[];
    netIncome: number;
    total: number;
  };
  balanced: boolean;
}

export interface IncomeStatementReport {
  startDate: Date;
  endDate: Date;
  income: {
    accounts: any[];
    total: number;
  };
  expenses: {
    accounts: any[];
    total: number;
  };
  netIncome: number;
}

@Injectable()
export class AccountsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private activityLogService: ActivityLogService,
  ) {}

  /**
   * Creates default accounts for a new business
   * This should be called when a new business is created
   */
  async createDefaultBusinessAccounts(
    businessId: string,
    createdBy: string = 'system',
  ): Promise<void> {
    const defaultAccounts = [
      {
        accountName: 'Uncategorized Income',
        accountNumber: '4000',
        accountCategory: AccountCategory.INCOME,
        accountType: ChartAccountType.INCOME,
        accountDetailType: AccountDetailType.SERVICE_FEE_INCOME,
        description: 'Default income account for uncategorized revenue',
        isSystemAccount: true,
      },
      {
        accountName: 'Uncategorized Expense',
        accountNumber: '5000',
        accountCategory: AccountCategory.EXPENSES,
        accountType: ChartAccountType.EXPENSES,
        accountDetailType: AccountDetailType.OTHER_MISCELLANEOUS_SERVICE_COST,
        description: 'Default expense account for uncategorized expenses',
        isSystemAccount: true,
      },
      {
        accountName: 'Uncategorized Asset',
        accountNumber: '1000',
        accountCategory: AccountCategory.ASSETS,
        accountType: ChartAccountType.CURRENT_ASSETS,
        accountDetailType: AccountDetailType.OTHER_CURRENT_ASSETS,
        description: 'Default asset account for uncategorized assets',
        isSystemAccount: true,
      },
      {
        accountName: 'Retained Earnings',
        accountNumber: '3000',
        accountCategory: AccountCategory.EQUITY,
        accountType: ChartAccountType.OWNERS_EQUITY,
        accountDetailType: AccountDetailType.OPENING_BALANCE_EQUITY,
        description: 'Accumulated profits and losses from previous periods',
        isSystemAccount: true,
      },
      {
        accountName: 'Services',
        accountNumber: '4100',
        accountCategory: AccountCategory.INCOME,
        accountType: ChartAccountType.INCOME,
        accountDetailType: AccountDetailType.SERVICE_FEE_INCOME,
        description: 'Income from services provided',
        isSystemAccount: false,
      },
      {
        accountName: 'Deferred Revenue',
        accountNumber: '2300',
        accountCategory: AccountCategory.LIABILITIES,
        accountType: ChartAccountType.CURRENT_LIABILITIES,
        accountDetailType: AccountDetailType.OTHER_CURRENT_LIABILITIES,
        description:
          'Revenue received in advance for services not yet provided',
        isSystemAccount: false,
      },
      {
        accountName: 'Billable Expense Income',
        accountNumber: '4200',
        accountCategory: AccountCategory.INCOME,
        accountType: ChartAccountType.INCOME,
        accountDetailType: AccountDetailType.SERVICE_FEE_INCOME,
        description: 'Income from reimbursable expenses charged to customers',
        isSystemAccount: false,
      },
      {
        accountName: 'Purchases',
        accountNumber: '5100',
        accountCategory: AccountCategory.EXPENSES,
        accountType: ChartAccountType.EXPENSES,
        accountDetailType: AccountDetailType.SUPPLIES_AND_MATERIALS,
        description: 'Direct purchases of goods and materials',
        isSystemAccount: false,
      },
      {
        accountName: 'Sales of Product Income',
        accountNumber: '4300',
        accountCategory: AccountCategory.INCOME,
        accountType: ChartAccountType.INCOME,
        accountDetailType: AccountDetailType.SALES_OF_PRODUCT_INCOME,
        description: 'Income from product sales',
        isSystemAccount: false,
      },
      {
        accountName: 'Cost of Sales',
        accountNumber: '5200',
        accountCategory: AccountCategory.EXPENSES,
        accountType: ChartAccountType.COST_OF_SALES,
        accountDetailType: AccountDetailType.SUPPLIES_MATERIALS_COS,
        description:
          'Direct costs attributable to the production of goods sold',
        isSystemAccount: false,
      },
      {
        accountName: 'Inventory Asset',
        accountNumber: '1200',
        accountCategory: AccountCategory.ASSETS,
        accountType: ChartAccountType.CURRENT_ASSETS,
        accountDetailType: AccountDetailType.INVENTORY,
        description: 'Value of goods held for sale',
        isSystemAccount: false,
      },
      {
        accountName: 'Opening Balance Equity',
        accountNumber: '3100',
        accountCategory: AccountCategory.EQUITY,
        accountType: ChartAccountType.OWNERS_EQUITY,
        accountDetailType: AccountDetailType.OPENING_BALANCE_EQUITY,
        description: 'Temporary account used to balance initial transactions',
        isSystemAccount: true,
      },
      {
        accountName: 'Undeposited Funds',
        accountNumber: '1100',
        accountCategory: AccountCategory.ASSETS,
        accountType: ChartAccountType.CURRENT_ASSETS,
        accountDetailType: AccountDetailType.UNDEPOSITED_FUNDS,
        description: 'Payments received but not yet deposited to bank',
        isSystemAccount: true,
      },
      {
        accountName: 'Accounts Receivable',
        accountNumber: '1300',
        accountCategory: AccountCategory.ASSETS,
        accountType: ChartAccountType.ACCOUNTS_RECEIVABLE,
        accountDetailType: AccountDetailType.ACCOUNT_RECEIVABLE,
        description: 'Money owed by customers for goods or services delivered',
        isSystemAccount: true,
      },
      {
        accountName: 'Accounts Payable',
        accountNumber: '2100',
        accountCategory: AccountCategory.LIABILITIES,
        accountType: ChartAccountType.ACCOUNTS_PAYABLE,
        accountDetailType: AccountDetailType.ACCOUNT_PAYABLE,
        description: 'Money owed to suppliers for goods or services received',
        isSystemAccount: true,
      },
      {
        accountName: 'Unapplied Cash Bill Payment Expense',
        accountNumber: '5300',
        accountCategory: AccountCategory.EXPENSES,
        accountType: ChartAccountType.EXPENSES,
        accountDetailType:
          AccountDetailType.UNAPPLIED_CASH_BILL_PAYMENT_EXPENSE,
        description:
          'Temporary account for supplier payments not yet applied to bills',
        isSystemAccount: true,
      },
    ];

    // Create accounts in batch
    for (const accountData of defaultAccounts) {
      try {
        // Check if account already exists
        const existingAccount = await this.db
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.businessId, businessId),
              eq(accounts.accountName, accountData.accountName),
              eq(accounts.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!existingAccount) {
          await this.db.insert(accounts).values({
            businessId,
            accountName: accountData.accountName,
            accountNumber: accountData.accountNumber,
            accountCategory: accountData.accountCategory as any,
            accountType: accountData.accountType as any,
            accountDetailType: accountData.accountDetailType as any,
            description: accountData.description,
            isSystemAccount: accountData.isSystemAccount,
            status: AccountStatus.ACTIVE,
            createdBy,
          });
        }
      } catch (error) {
        console.error(
          `Error creating account ${accountData.accountName}:`,
          error,
        );
        // Continue with other accounts even if one fails
      }
    }
  }

  /**
   * Creates additional system accounts based on business type
   */
  async createBusinessTypeSpecificAccounts(
    businessId: string,
    businessType: 'SERVICE' | 'RETAIL' | 'MANUFACTURING' | 'NON_PROFIT',
    createdBy: string = 'system',
  ): Promise<void> {
    let additionalAccounts = [];

    switch (businessType) {
      case 'RETAIL':
        additionalAccounts = [
          {
            accountName: 'Merchandise Inventory',
            accountNumber: '1201',
            accountCategory: AccountCategory.ASSETS,
            accountType: ChartAccountType.CURRENT_ASSETS,
            accountDetailType: AccountDetailType.INVENTORY,
            description: 'Retail inventory held for sale',
          },
          {
            accountName: 'Credit Card Processing Fees',
            accountNumber: '5401',
            accountCategory: AccountCategory.EXPENSES,
            accountType: ChartAccountType.EXPENSES,
            accountDetailType: AccountDetailType.BANK_CHARGES,
            description: 'Fees charged by credit card processors',
          },
        ];
        break;

      case 'MANUFACTURING':
        additionalAccounts = [
          {
            accountName: 'Raw Materials',
            accountNumber: '1202',
            accountCategory: AccountCategory.ASSETS,
            accountType: ChartAccountType.CURRENT_ASSETS,
            accountDetailType: AccountDetailType.INVENTORY,
            description: 'Raw materials inventory',
          },
          {
            accountName: 'Work in Process',
            accountNumber: '1203',
            accountCategory: AccountCategory.ASSETS,
            accountType: ChartAccountType.CURRENT_ASSETS,
            accountDetailType: AccountDetailType.INVENTORY,
            description: 'Partially completed goods',
          },
          {
            accountName: 'Finished Goods',
            accountNumber: '1204',
            accountCategory: AccountCategory.ASSETS,
            accountType: ChartAccountType.CURRENT_ASSETS,
            accountDetailType: AccountDetailType.INVENTORY,
            description: 'Completed products ready for sale',
          },
          {
            accountName: 'Direct Labor',
            accountNumber: '5201',
            accountCategory: AccountCategory.EXPENSES,
            accountType: ChartAccountType.COST_OF_SALES,
            accountDetailType: AccountDetailType.COST_OF_LABOR_COS,
            description: 'Direct labor costs in production',
          },
        ];
        break;

      case 'NON_PROFIT':
        additionalAccounts = [
          {
            accountName: 'Donations Received',
            accountNumber: '4400',
            accountCategory: AccountCategory.INCOME,
            accountType: ChartAccountType.INCOME,
            accountDetailType: AccountDetailType.NON_PROFIT_INCOME,
            description: 'Charitable donations received',
          },
          {
            accountName: 'Grant Income',
            accountNumber: '4401',
            accountCategory: AccountCategory.INCOME,
            accountType: ChartAccountType.INCOME,
            accountDetailType: AccountDetailType.NON_PROFIT_INCOME,
            description: 'Income from grants',
          },
          {
            accountName: 'Program Service Expenses',
            accountNumber: '5500',
            accountCategory: AccountCategory.EXPENSES,
            accountType: ChartAccountType.EXPENSES,
            accountDetailType:
              AccountDetailType.OTHER_MISCELLANEOUS_SERVICE_COST,
            description: 'Expenses for program services',
          },
        ];
        break;
    }

    // Create additional accounts
    for (const accountData of additionalAccounts) {
      try {
        await this.db.insert(accounts).values({
          businessId,
          accountName: accountData.accountName,
          accountNumber: accountData.accountNumber,
          accountCategory: accountData.accountCategory,
          accountType: accountData.accountType,
          accountDetailType: accountData.accountDetailType,
          description: accountData.description,
          isSystemAccount: false,
          status: AccountStatus.ACTIVE,
          createdBy,
        });
      } catch (error) {
        console.error(
          `Error creating account ${accountData.accountName}:`,
          error,
        );
      }
    }
  }

  async create(
    userId: string,
    businessId: string | null,
    createAccountDto: CreateAccountDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if an account with the same name already exists for this business
      const existingAccount = await this.db
        .select()
        .from(accounts)
        .where(
          and(
            eq(accounts.businessId, businessId),
            ilike(accounts.accountName, createAccountDto.accountName),
            eq(accounts.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingAccount) {
        throw new ConflictException(
          `Account with name "${createAccountDto.accountName}" already exists`,
        );
      }

      // Check if account number is provided and unique
      if (createAccountDto.accountNumber) {
        const existingAccountNumber = await this.db
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.businessId, businessId),
              eq(accounts.accountNumber, createAccountDto.accountNumber),
              eq(accounts.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingAccountNumber) {
          throw new ConflictException(
            `Account with number "${createAccountDto.accountNumber}" already exists`,
          );
        }
      }

      // Validate parent account if provided
      if (createAccountDto.parentAccountId) {
        const parentAccount = await this.db
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.id, createAccountDto.parentAccountId),
              eq(accounts.businessId, businessId),
              eq(accounts.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!parentAccount) {
          throw new BadRequestException('Parent account not found');
        }
      }

      // Validate income account if provided
      if (createAccountDto.incomeAccountId) {
        const incomeAccount = await this.db
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.id, createAccountDto.incomeAccountId),
              eq(accounts.businessId, businessId),
              eq(accounts.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!incomeAccount) {
          throw new BadRequestException('Income account not found');
        }
      }

      const [account] = await this.db
        .insert(accounts)
        .values({
          businessId,
          accountName: createAccountDto.accountName,
          accountNumber: createAccountDto.accountNumber,
          accountCategory: createAccountDto.accountCategory as any,
          accountType: createAccountDto.accountType as any,
          accountDetailType: createAccountDto.accountDetailType as any,
          parentAccountId: createAccountDto.parentAccountId,
          openingBalance: createAccountDto.openingBalance,
          openingBalanceDate: createAccountDto.openingBalanceDate,
          description: createAccountDto.description,
          defaultTaxId: createAccountDto.defaultTaxId,
          useForBillableExpenses:
            createAccountDto.useForBillableExpenses ?? false,
          incomeAccountId: createAccountDto.incomeAccountId,
          isSystemAccount: createAccountDto.isSystemAccount ?? false,
          status: createAccountDto.status ?? AccountStatus.ACTIVE,
          createdBy: userId,
        })
        .returning();

      // Log the activity
      await this.activityLogService.logCreate(
        account.id,
        EntityType.ACCOUNT,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id: account.id };
    } catch (error) {
      console.error('Failed to create account:', error);
      if (
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create account');
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createAccountDto: CreateAccountDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    return this.create(userId, businessId, createAccountDto, metadata);
  }

  async findAll(
    _userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    accountName?: string,
    accountNumber?: string,
    accountCategory?: string,
    accountType?: string,
    accountDetailType?: string,
    status?: string,
    _filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: AccountListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(accounts.isDeleted, false),
      eq(accounts.businessId, businessId),
    ];

    // Add search filters
    if (accountName) {
      whereConditions.push(ilike(accounts.accountName, `%${accountName}%`));
    }

    if (accountNumber) {
      whereConditions.push(ilike(accounts.accountNumber, `%${accountNumber}%`));
    }

    if (accountCategory) {
      whereConditions.push(
        eq(accounts.accountCategory, accountCategory as any),
      );
    }

    if (accountType) {
      whereConditions.push(ilike(accounts.accountType, `%${accountType}%`));
    }

    if (accountDetailType) {
      whereConditions.push(
        eq(accounts.accountDetailType, accountDetailType as any),
      );
    }

    if (status) {
      whereConditions.push(eq(accounts.status, status as AccountStatus));
    }

    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(accounts.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(accounts.createdAt, toDate));
      }
    }

    // Add advanced filters if provided
    if (_filters) {
      try {
        const parsedFilters = JSON.parse(_filters);
        const filterConditions = [];

        for (const filter of parsedFilters) {
          const { id: fieldId, value, operator } = filter;

          if (fieldId === 'accountName') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(accounts.accountName, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(accounts.accountName, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(accounts.accountName, value));
                break;
              case 'ne':
                filterConditions.push(sql`${accounts.accountName} != ${value}`);
                break;
              case 'isEmpty':
                filterConditions.push(
                  or(
                    isNull(accounts.accountName),
                    eq(accounts.accountName, ''),
                  ),
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  and(
                    isNotNull(accounts.accountName),
                    sql`${accounts.accountName} != ''`,
                  ),
                );
                break;
            }
          } else if (fieldId === 'accountNumber') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(accounts.accountNumber, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(accounts.accountNumber, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(accounts.accountNumber, value));
                break;
              case 'ne':
                filterConditions.push(
                  sql`${accounts.accountNumber} != ${value}`,
                );
                break;
              case 'isEmpty':
                filterConditions.push(
                  or(
                    isNull(accounts.accountNumber),
                    eq(accounts.accountNumber, ''),
                  ),
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  and(
                    isNotNull(accounts.accountNumber),
                    sql`${accounts.accountNumber} != ''`,
                  ),
                );
                break;
            }
          } else if (fieldId === 'accountCategory') {
            switch (operator) {
              case 'eq':
                filterConditions.push(eq(accounts.accountCategory, value));
                break;
              case 'ne':
                filterConditions.push(
                  sql`${accounts.accountCategory} != ${value}`,
                );
                break;
            }
          } else if (fieldId === 'accountType') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(accounts.accountType, `%${value}%`),
                );
                break;
              case 'eq':
                filterConditions.push(eq(accounts.accountType, value));
                break;
              case 'ne':
                filterConditions.push(sql`${accounts.accountType} != ${value}`);
                break;
            }
          } else if (fieldId === 'accountDetailType') {
            switch (operator) {
              case 'eq':
                filterConditions.push(eq(accounts.accountDetailType, value));
                break;
              case 'ne':
                filterConditions.push(
                  sql`${accounts.accountDetailType} != ${value}`,
                );
                break;
            }
          } else if (fieldId === 'status') {
            switch (operator) {
              case 'eq':
                filterConditions.push(eq(accounts.status, value));
                break;
              case 'ne':
                filterConditions.push(sql`${accounts.status} != ${value}`);
                break;
            }
          }
        }

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            whereConditions.push(or(...filterConditions));
          } else {
            whereConditions.push(and(...filterConditions));
          }
        }
      } catch {
        // Invalid JSON, ignore filters
      }
    }

    // Build final where clause
    const whereClause = and(...whereConditions);

    // Build order by clause
    let orderBy = [desc(accounts.createdAt), asc(accounts.id)]; // Default sort

    if (sort) {
      try {
        const parsedSort = JSON.parse(sort);
        if (parsedSort.length > 0) {
          const sortField = parsedSort[0];
          const isDesc = sortField.desc === true;

          switch (sortField.id) {
            case 'accountName':
              orderBy = [
                isDesc ? desc(accounts.accountName) : asc(accounts.accountName),
                asc(accounts.id), // Secondary sort for consistency
              ];
              break;
            case 'accountNumber':
              orderBy = [
                isDesc
                  ? desc(accounts.accountNumber)
                  : asc(accounts.accountNumber),
                asc(accounts.id),
              ];
              break;
            case 'accountCategory':
              orderBy = [
                isDesc
                  ? desc(accounts.accountCategory)
                  : asc(accounts.accountCategory),
                asc(accounts.id),
              ];
              break;
            case 'accountType':
              orderBy = [
                isDesc ? desc(accounts.accountType) : asc(accounts.accountType),
                asc(accounts.id),
              ];
              break;
            case 'openingBalance':
              orderBy = [
                isDesc
                  ? desc(accounts.openingBalance)
                  : asc(accounts.openingBalance),
                asc(accounts.id),
              ];
              break;
            case 'createdAt':
              orderBy = [
                isDesc ? desc(accounts.createdAt) : asc(accounts.createdAt),
                asc(accounts.id),
              ];
              break;
            case 'updatedAt':
              orderBy = [
                isDesc ? desc(accounts.updatedAt) : asc(accounts.updatedAt),
                asc(accounts.id),
              ];
              break;
          }
        }
      } catch {
        // Invalid JSON, use default sort
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(accounts)
      .where(whereClause);

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Get paginated results
    const results = await this.db
      .select({
        id: accounts.id,
        accountName: accounts.accountName,
        accountNumber: accounts.accountNumber,
        accountCategory: accounts.accountCategory,
        accountType: accounts.accountType,
        accountDetailType: accounts.accountDetailType,
        parentAccountId: accounts.parentAccountId,
        openingBalance: accounts.openingBalance,
        description: accounts.description,
        useForBillableExpenses: accounts.useForBillableExpenses,
        isSystemAccount: accounts.isSystemAccount,
        status: accounts.status,
      })
      .from(accounts)
      .where(whereClause)
      .orderBy(...orderBy)
      .limit(limit)
      .offset(offset);

    return {
      data: results as AccountListDto[],
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    accountName?: string,
    accountNumber?: string,
    accountCategory?: string,
    accountType?: string,
    accountDetailType?: string,
    status?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: AccountListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    return this.findAll(
      userId,
      businessId,
      page,
      limit,
      from,
      to,
      accountName,
      accountNumber,
      accountCategory,
      accountType,
      accountDetailType,
      status,
      filters,
      joinOperator,
      sort,
    );
  }

  async findOne(
    _userId: string,
    businessId: string | null,
    id: string,
  ): Promise<AccountDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const result = await this.db
      .select({
        id: accounts.id,
        businessId: accounts.businessId,
        accountName: accounts.accountName,
        accountNumber: accounts.accountNumber,
        accountCategory: accounts.accountCategory,
        accountType: accounts.accountType,
        accountDetailType: accounts.accountDetailType,
        parentAccountId: accounts.parentAccountId,
        openingBalance: accounts.openingBalance,
        openingBalanceDate: accounts.openingBalanceDate,
        description: accounts.description,
        defaultTaxId: accounts.defaultTaxId,
        useForBillableExpenses: accounts.useForBillableExpenses,
        incomeAccountId: accounts.incomeAccountId,
        isSystemAccount: accounts.isSystemAccount,
        status: accounts.status,
        createdBy: users.name,
        updatedBy: sql<string>`updated_user.name`,
        createdAt: accounts.createdAt,
        updatedAt: accounts.updatedAt,
      })
      .from(accounts)
      .leftJoin(users, eq(accounts.createdBy, users.id))
      .leftJoin(
        sql`${users} as updated_user`,
        eq(accounts.updatedBy, sql`updated_user.id`),
      )
      .where(
        and(
          eq(accounts.id, id),
          eq(accounts.businessId, businessId),
          eq(accounts.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!result) {
      throw new NotFoundException('Account not found');
    }

    return result as AccountDto;
  }

  async findAllSlim(
    _userId: string,
    businessId: string | null,
  ): Promise<AccountSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const results = await this.db
      .select({
        id: accounts.id,
        accountName: accounts.accountName,
        accountNumber: accounts.accountNumber,
        accountCategory: accounts.accountCategory,
        accountType: accounts.accountType,
        status: accounts.status,
      })
      .from(accounts)
      .where(
        and(
          eq(accounts.businessId, businessId),
          eq(accounts.status, AccountStatus.ACTIVE),
          eq(accounts.isDeleted, false),
        ),
      )
      .orderBy(asc(accounts.accountName));

    return results as AccountSlimDto[];
  }

  async checkAccountNameAvailability(
    _userId: string,
    businessId: string | null,
    accountName: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingAccount = await this.db
      .select()
      .from(accounts)
      .where(
        and(
          eq(accounts.businessId, businessId),
          ilike(accounts.accountName, accountName),
          eq(accounts.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingAccount };
  }

  async checkAccountNumberAvailability(
    _userId: string,
    businessId: string | null,
    accountNumber: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingAccount = await this.db
      .select()
      .from(accounts)
      .where(
        and(
          eq(accounts.businessId, businessId),
          eq(accounts.accountNumber, accountNumber),
          eq(accounts.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingAccount };
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateAccountDto: UpdateAccountDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if account exists
      const existingAccount = await this.db
        .select()
        .from(accounts)
        .where(
          and(
            eq(accounts.id, id),
            eq(accounts.businessId, businessId),
            eq(accounts.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingAccount) {
        throw new NotFoundException('Account not found');
      }

      // Check if account name is being updated and is unique
      if (
        updateAccountDto.accountName &&
        updateAccountDto.accountName !== existingAccount.accountName
      ) {
        const duplicateAccount = await this.db
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.businessId, businessId),
              ilike(accounts.accountName, updateAccountDto.accountName),
              eq(accounts.isDeleted, false),
              sql`${accounts.id} != ${id}`,
            ),
          )
          .then((results) => results[0]);

        if (duplicateAccount) {
          throw new ConflictException(
            `Account with name "${updateAccountDto.accountName}" already exists`,
          );
        }
      }

      // Check if account number is being updated and is unique
      if (
        updateAccountDto.accountNumber &&
        updateAccountDto.accountNumber !== existingAccount.accountNumber
      ) {
        const duplicateAccountNumber = await this.db
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.businessId, businessId),
              eq(accounts.accountNumber, updateAccountDto.accountNumber),
              eq(accounts.isDeleted, false),
              sql`${accounts.id} != ${id}`,
            ),
          )
          .then((results) => results[0]);

        if (duplicateAccountNumber) {
          throw new ConflictException(
            `Account with number "${updateAccountDto.accountNumber}" already exists`,
          );
        }
      }

      // Validate parent account if provided
      if (updateAccountDto.parentAccountId) {
        const parentAccount = await this.db
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.id, updateAccountDto.parentAccountId),
              eq(accounts.businessId, businessId),
              eq(accounts.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!parentAccount) {
          throw new BadRequestException('Parent account not found');
        }
      }

      // Validate income account if provided
      if (updateAccountDto.incomeAccountId) {
        const incomeAccount = await this.db
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.id, updateAccountDto.incomeAccountId),
              eq(accounts.businessId, businessId),
              eq(accounts.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!incomeAccount) {
          throw new BadRequestException('Income account not found');
        }
      }

      const updateData: any = {
        ...updateAccountDto,
        updatedBy: userId,
        updatedAt: new Date(),
      };

      // Cast enum values to their string equivalents for database compatibility
      if (updateAccountDto.accountCategory) {
        updateData.accountCategory = updateAccountDto.accountCategory as any;
      }
      if (updateAccountDto.accountType) {
        updateData.accountType = updateAccountDto.accountType as any;
      }
      if (updateAccountDto.accountDetailType) {
        updateData.accountDetailType =
          updateAccountDto.accountDetailType as any;
      }

      const [updatedAccount] = await this.db
        .update(accounts)
        .set(updateData)
        .where(eq(accounts.id, id))
        .returning();

      // Log the activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.ACCOUNT,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id: updatedAccount.id };
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to update account');
    }
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateAccountDto: UpdateAccountDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    return this.update(userId, businessId, id, updateAccountDto, metadata);
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if account exists
    const existingAccount = await this.db
      .select()
      .from(accounts)
      .where(
        and(
          eq(accounts.id, id),
          eq(accounts.businessId, businessId),
          eq(accounts.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingAccount) {
      throw new NotFoundException('Account not found');
    }

    // Soft delete the account
    await this.db
      .update(accounts)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(accounts.id, id));

    // Log the activity
    await this.activityLogService.logDelete(
      id,
      EntityType.ACCOUNT,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      id,
      message: 'Account deleted successfully',
    };
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createAccountDtos: CreateAccountDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const createdIds: string[] = [];

    // Use transaction for bulk operations
    await this.db.transaction(async (tx) => {
      for (const createAccountDto of createAccountDtos) {
        // Check if account name already exists
        const existingAccount = await tx
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.businessId, businessId),
              ilike(accounts.accountName, createAccountDto.accountName),
              eq(accounts.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingAccount) {
          throw new ConflictException(
            `Account with name "${createAccountDto.accountName}" already exists`,
          );
        }

        // Check if account number already exists (if provided)
        if (createAccountDto.accountNumber) {
          const existingAccountNumber = await tx
            .select()
            .from(accounts)
            .where(
              and(
                eq(accounts.businessId, businessId),
                eq(accounts.accountNumber, createAccountDto.accountNumber),
                eq(accounts.isDeleted, false),
              ),
            )
            .then((results) => results[0]);

          if (existingAccountNumber) {
            throw new ConflictException(
              `Account with number "${createAccountDto.accountNumber}" already exists`,
            );
          }
        }

        const [account] = await tx
          .insert(accounts)
          .values({
            businessId,
            accountName: createAccountDto.accountName,
            accountNumber: createAccountDto.accountNumber,
            accountCategory: createAccountDto.accountCategory as any,
            accountType: createAccountDto.accountType as any,
            accountDetailType: createAccountDto.accountDetailType as any,
            parentAccountId: createAccountDto.parentAccountId,
            openingBalance: createAccountDto.openingBalance,
            openingBalanceDate: createAccountDto.openingBalanceDate,
            description: createAccountDto.description,
            defaultTaxId: createAccountDto.defaultTaxId,
            useForBillableExpenses:
              createAccountDto.useForBillableExpenses ?? false,
            incomeAccountId: createAccountDto.incomeAccountId,
            isSystemAccount: createAccountDto.isSystemAccount ?? false,
            status: createAccountDto.status ?? AccountStatus.ACTIVE,
            createdBy: userId,
          })
          .returning();

        createdIds.push(account.id);
      }
    });

    // Log bulk create operation
    await this.activityLogService.logBulkOperation(
      ActivityType.BULK_CREATE,
      EntityType.ACCOUNT,
      createdIds,
      { bulkCreateCount: createdIds.length },
      userId,
      businessId,
      {
        filterCriteria: { bulkAccountsCount: createdIds.length },
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return { ids: createdIds };
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createAccountDtos: CreateAccountDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    return this.bulkCreate(userId, businessId, createAccountDtos, metadata);
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    ids: string[],
    metadata?: ActivityMetadata,
  ): Promise<{ deletedCount: number; deletedIds: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const deletedIds: string[] = [];

    // Use transaction for bulk operations
    await this.db.transaction(async (tx) => {
      for (const id of ids) {
        // Check if account exists
        const existingAccount = await tx
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.id, id),
              eq(accounts.businessId, businessId),
              eq(accounts.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingAccount) {
          await tx
            .update(accounts)
            .set({
              isDeleted: true,
              updatedBy: userId,
              updatedAt: new Date(),
            })
            .where(eq(accounts.id, id));

          deletedIds.push(id);
        }
      }
    });

    // Log bulk delete operation
    await this.activityLogService.logBulkOperation(
      ActivityType.BULK_DELETE,
      EntityType.ACCOUNT,
      deletedIds,
      { bulkDeleteCount: deletedIds.length },
      userId,
      businessId,
      {
        filterCriteria: { accountIds: ids },
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      deletedCount: deletedIds.length,
      deletedIds,
    };
  }

  async findFilteredAccounts(
    _userId: string,
    businessId: string | null,
    accountCategory?: string,
    accountType?: string,
    accountDetailType?: string,
  ): Promise<
    {
      id: string;
      accountName: string;
      accountCategory: AccountCategory;
      accountType: ChartAccountType;
      accountDetailType: AccountDetailType;
    }[]
  > {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Build base where conditions
    const whereConditions = [
      eq(accounts.isDeleted, false),
      eq(accounts.businessId, businessId),
    ];

    // Add filter conditions based on provided parameters
    if (accountCategory) {
      whereConditions.push(
        eq(accounts.accountCategory, accountCategory as any),
      );
    }

    if (accountType) {
      whereConditions.push(eq(accounts.accountType, accountType as any));
    }

    if (accountDetailType) {
      whereConditions.push(
        eq(accounts.accountDetailType, accountDetailType as any),
      );
    }

    // Execute query with only required fields for optimization
    const results = await this.db
      .select({
        id: accounts.id,
        accountName: accounts.accountName,
        accountCategory: accounts.accountCategory,
        accountType: accounts.accountType,
        accountDetailType: accounts.accountDetailType,
      })
      .from(accounts)
      .where(and(...whereConditions))
      .orderBy(accounts.accountName);

    return results as {
      id: string;
      accountName: string;
      accountCategory: AccountCategory;
      accountType: ChartAccountType;
      accountDetailType: AccountDetailType;
    }[];
  }

  // Account Balance Methods
  async getAccountBalance(
    businessId: string,
    accountId: string,
    asOfDate?: Date,
  ): Promise<AccountBalance> {
    const account = await this.db
      .select()
      .from(accounts)
      .where(
        and(eq(accounts.id, accountId), eq(accounts.businessId, businessId)),
      )
      .then((results) => results[0]);

    if (!account) {
      throw new NotFoundException('Account not found');
    }

    const cutoffDate = asOfDate || new Date();

    const balanceQuery = this.db
      .select({
        totalDebits: sum(journalEntryLines.debitAmount),
        totalCredits: sum(journalEntryLines.creditAmount),
      })
      .from(journalEntryLines)
      .leftJoin(
        journalEntries,
        eq(journalEntryLines.journalId, journalEntries.id),
      )
      .where(
        and(
          eq(journalEntryLines.accountId, accountId),
          eq(journalEntryLines.businessId, businessId),
          eq(journalEntries.status, JournalEntryStatus.POSTED),
          lte(
            journalEntries.journalDate,
            cutoffDate.toISOString().split('T')[0],
          ),
        ),
      );

    const result = await balanceQuery.then((results) => results[0]);

    const debitBalance = Number(result?.totalDebits || 0);
    const creditBalance = Number(result?.totalCredits || 0);
    const netBalance = debitBalance - creditBalance;

    return {
      accountId,
      debitBalance,
      creditBalance,
      netBalance,
      asOfDate: cutoffDate,
    };
  }

  async getAccountBalances(
    businessId: string,
    accountIds: string[],
    asOfDate?: Date,
  ): Promise<AccountBalance[]> {
    const balances = [];

    for (const accountId of accountIds) {
      const balance = await this.getAccountBalance(
        businessId,
        accountId,
        asOfDate,
      );
      balances.push(balance);
    }

    return balances;
  }

  async getTrialBalance(
    businessId: string,
    asOfDate?: Date,
  ): Promise<TrialBalanceReport> {
    const cutoffDate = asOfDate || new Date();

    const businessAccounts = await this.db
      .select({
        id: accounts.id,
        accountNumber: accounts.accountNumber,
        accountName: accounts.accountName,
        accountCategory: accounts.accountCategory,
        accountType: accounts.accountType,
      })
      .from(accounts)
      .where(
        and(eq(accounts.businessId, businessId), eq(accounts.isDeleted, false)),
      )
      .orderBy(accounts.accountNumber);

    const trialBalanceAccounts: TrialBalanceAccount[] = [];
    let totalDebits = 0;
    let totalCredits = 0;

    for (const account of businessAccounts) {
      const balance = await this.getAccountBalance(
        businessId,
        account.id,
        cutoffDate,
      );

      trialBalanceAccounts.push({
        id: account.id,
        accountNumber: account.accountNumber || '',
        accountName: account.accountName,
        accountCategory: account.accountCategory,
        accountType: account.accountType,
        debitBalance: balance.debitBalance,
        creditBalance: balance.creditBalance,
        netBalance: balance.netBalance,
      });

      totalDebits += balance.debitBalance;
      totalCredits += balance.creditBalance;
    }

    return {
      asOfDate: cutoffDate,
      accounts: trialBalanceAccounts,
      totalDebits,
      totalCredits,
      isBalanced: Math.abs(totalDebits - totalCredits) < 0.01,
    };
  }

  async getAccountsByCategory(
    businessId: string,
    category: AccountCategory,
    asOfDate?: Date,
  ): Promise<TrialBalanceAccount[]> {
    const businessAccounts = await this.db
      .select({
        id: accounts.id,
        accountNumber: accounts.accountNumber,
        accountName: accounts.accountName,
        accountCategory: accounts.accountCategory,
        accountType: accounts.accountType,
      })
      .from(accounts)
      .where(
        and(
          eq(accounts.businessId, businessId),
          eq(accounts.accountCategory, category as any),
          eq(accounts.isDeleted, false),
        ),
      )
      .orderBy(accounts.accountNumber);

    const categoryAccounts: TrialBalanceAccount[] = [];

    for (const account of businessAccounts) {
      const balance = await this.getAccountBalance(
        businessId,
        account.id,
        asOfDate,
      );

      categoryAccounts.push({
        id: account.id,
        accountNumber: account.accountNumber || '',
        accountName: account.accountName,
        accountCategory: account.accountCategory,
        accountType: account.accountType,
        debitBalance: balance.debitBalance,
        creditBalance: balance.creditBalance,
        netBalance: balance.netBalance,
      });
    }

    return categoryAccounts;
  }

  async getNetWorth(businessId: string, asOfDate?: Date): Promise<number> {
    const [assets, liabilities] = await Promise.all([
      this.getAccountsByCategory(businessId, AccountCategory.ASSETS, asOfDate),
      this.getAccountsByCategory(
        businessId,
        AccountCategory.LIABILITIES,
        asOfDate,
      ),
    ]);

    const totalAssets = assets.reduce(
      (sum, account) => sum + account.netBalance,
      0,
    );
    const totalLiabilities = liabilities.reduce(
      (sum, account) => sum + Math.abs(account.netBalance),
      0,
    );

    return totalAssets - totalLiabilities;
  }

  async getAccountHistory(
    businessId: string,
    accountId: string,
    startDate: Date,
    endDate: Date,
    limit = 100,
  ): Promise<any[]> {
    const transactions = await this.db
      .select({
        journalId: journalEntryLines.journalId,
        journalNumber: journalEntries.journalNumber,
        journalDate: journalEntries.journalDate,
        description: journalEntries.description,
        debitAmount: journalEntryLines.debitAmount,
        creditAmount: journalEntryLines.creditAmount,
        lineDescription: journalEntryLines.description,
      })
      .from(journalEntryLines)
      .leftJoin(
        journalEntries,
        eq(journalEntryLines.journalId, journalEntries.id),
      )
      .where(
        and(
          eq(journalEntryLines.accountId, accountId),
          eq(journalEntryLines.businessId, businessId),
          eq(journalEntries.status, JournalEntryStatus.POSTED),
          gte(
            journalEntries.journalDate,
            startDate.toISOString().split('T')[0],
          ),
          lte(journalEntries.journalDate, endDate.toISOString().split('T')[0]),
        ),
      )
      .orderBy(desc(journalEntries.journalDate))
      .limit(limit);

    return transactions;
  }

  // Reports Methods
  async getBalanceSheet(
    businessId: string,
    asOfDate?: Date,
  ): Promise<BalanceSheetReport> {
    const cutoffDate = asOfDate || new Date();
    const trialBalance = await this.getTrialBalance(businessId, cutoffDate);

    const assets = trialBalance.accounts.filter(
      (acc) => acc.accountCategory === AccountCategory.ASSETS,
    );
    const liabilities = trialBalance.accounts.filter(
      (acc) => acc.accountCategory === AccountCategory.LIABILITIES,
    );
    const equity = trialBalance.accounts.filter(
      (acc) => acc.accountCategory === AccountCategory.EQUITY,
    );

    const totalAssets = assets.reduce((sum, acc) => sum + acc.netBalance, 0);
    const totalLiabilities = liabilities.reduce(
      (sum, acc) => sum + Math.abs(acc.netBalance),
      0,
    );
    const totalEquity = equity.reduce(
      (sum, acc) => sum + Math.abs(acc.netBalance),
      0,
    );

    const netIncome = await this.calculateNetIncome(businessId, cutoffDate);
    const totalEquityWithNetIncome = totalEquity + netIncome;

    return {
      asOfDate: cutoffDate,
      assets: {
        current: assets.filter((acc) => this.isCurrentAsset(acc.accountType)),
        nonCurrent: assets.filter(
          (acc) => !this.isCurrentAsset(acc.accountType),
        ),
        total: totalAssets,
      },
      liabilities: {
        current: liabilities.filter((acc) =>
          this.isCurrentLiability(acc.accountType),
        ),
        nonCurrent: liabilities.filter(
          (acc) => !this.isCurrentLiability(acc.accountType),
        ),
        total: totalLiabilities,
      },
      equity: {
        accounts: equity,
        netIncome,
        total: totalEquityWithNetIncome,
      },
      balanced:
        Math.abs(totalAssets - (totalLiabilities + totalEquityWithNetIncome)) <
        0.01,
    };
  }

  async getIncomeStatement(
    businessId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<IncomeStatementReport> {
    const income = await this.getAccountsByCategory(
      businessId,
      AccountCategory.REVENUE,
      endDate,
    );
    const expenses = await this.getAccountsByCategory(
      businessId,
      AccountCategory.EXPENSES,
      endDate,
    );

    const totalIncome = income.reduce(
      (sum, acc) => sum + Math.abs(acc.netBalance),
      0,
    );
    const totalExpenses = expenses.reduce(
      (sum, acc) => sum + Math.abs(acc.netBalance),
      0,
    );
    const netIncome = totalIncome - totalExpenses;

    return {
      startDate,
      endDate,
      income: {
        accounts: income,
        total: totalIncome,
      },
      expenses: {
        accounts: expenses,
        total: totalExpenses,
      },
      netIncome,
    };
  }

  async getAccountSummary(businessId: string, asOfDate?: Date) {
    const cutoffDate = asOfDate || new Date();

    const [assets, liabilities, equity, revenue, expenses] = await Promise.all([
      this.getAccountsByCategory(
        businessId,
        AccountCategory.ASSETS,
        cutoffDate,
      ),
      this.getAccountsByCategory(
        businessId,
        AccountCategory.LIABILITIES,
        cutoffDate,
      ),
      this.getAccountsByCategory(
        businessId,
        AccountCategory.EQUITY,
        cutoffDate,
      ),
      this.getAccountsByCategory(
        businessId,
        AccountCategory.REVENUE,
        cutoffDate,
      ),
      this.getAccountsByCategory(
        businessId,
        AccountCategory.EXPENSES,
        cutoffDate,
      ),
    ]);

    return {
      totalAssets: assets.reduce((sum, acc) => sum + acc.netBalance, 0),
      totalLiabilities: liabilities.reduce(
        (sum, acc) => sum + Math.abs(acc.netBalance),
        0,
      ),
      totalEquity: equity.reduce(
        (sum, acc) => sum + Math.abs(acc.netBalance),
        0,
      ),
      totalRevenue: revenue.reduce(
        (sum, acc) => sum + Math.abs(acc.netBalance),
        0,
      ),
      totalExpenses: expenses.reduce(
        (sum, acc) => sum + Math.abs(acc.netBalance),
        0,
      ),
      netWorth: await this.getNetWorth(businessId, cutoffDate),
    };
  }

  private async calculateNetIncome(
    businessId: string,
    asOfDate: Date,
  ): Promise<number> {
    const yearStart = new Date(asOfDate.getFullYear(), 0, 1);
    const incomeStatement = await this.getIncomeStatement(
      businessId,
      yearStart,
      asOfDate,
    );
    return incomeStatement.netIncome;
  }

  private isCurrentAsset(accountType: string): boolean {
    const currentAssetTypes = [
      ChartAccountType.CASH_AND_CASH_EQUIVALENTS,
      ChartAccountType.ACCOUNTS_RECEIVABLE,
      ChartAccountType.CURRENT_ASSETS,
    ];
    return currentAssetTypes.includes(accountType as ChartAccountType);
  }

  private isCurrentLiability(accountType: string): boolean {
    const currentLiabilityTypes = [
      ChartAccountType.ACCOUNTS_PAYABLE,
      ChartAccountType.CREDIT_CARD,
      ChartAccountType.CURRENT_LIABILITIES,
    ];
    return currentLiabilityTypes.includes(accountType as ChartAccountType);
  }

  // Journal Entries Methods
  async createJournalEntry(
    userId: string,
    businessId: string | null,
    createJournalEntryDto: CreateJournalEntryDto,
    metadata?: ActivityMetadata,
  ): Promise<JournalEntryDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Validate that debits equal credits
      const totalDebits = createJournalEntryDto.journalEntryLines.reduce(
        (sum, line) => sum + parseFloat(line.debitAmount),
        0,
      );
      const totalCredits = createJournalEntryDto.journalEntryLines.reduce(
        (sum, line) => sum + parseFloat(line.creditAmount),
        0,
      );

      if (Math.abs(totalDebits - totalCredits) > 0.01) {
        throw new BadRequestException(
          'Journal entry is not balanced: total debits must equal total credits',
        );
      }

      // Check if journal number already exists
      const existingJournalEntry = await this.db
        .select()
        .from(journalEntries)
        .where(
          and(
            eq(journalEntries.businessId, businessId),
            eq(
              journalEntries.journalNumber,
              createJournalEntryDto.journalNumber,
            ),
            isNull(journalEntries.isDeleted),
          ),
        )
        .limit(1);

      if (existingJournalEntry.length > 0) {
        throw new ConflictException(
          `Journal number '${createJournalEntryDto.journalNumber}' already exists`,
        );
      }

      // Validate that all accounts exist
      const accountIds = createJournalEntryDto.journalEntryLines.map(
        (line) => line.accountId,
      );
      const existingAccounts = await this.db
        .select({ id: accounts.id })
        .from(accounts)
        .where(
          and(
            inArray(accounts.id, accountIds),
            eq(accounts.businessId, businessId),
            eq(accounts.isDeleted, false),
          ),
        );

      if (existingAccounts.length !== accountIds.length) {
        throw new BadRequestException('One or more accounts do not exist');
      }

      // Create journal entry and lines in a transaction
      const result = await this.db.transaction(async (tx) => {
        // Create journal entry
        const [journalEntry] = await tx
          .insert(journalEntries)
          .values({
            businessId,
            journalNumber: createJournalEntryDto.journalNumber,
            journalDate: createJournalEntryDto.journalDate,
            description: createJournalEntryDto.description,
            memo: createJournalEntryDto.memo,
            referenceType: createJournalEntryDto.referenceType,
            referenceId: createJournalEntryDto.referenceId,
            status: createJournalEntryDto.status || JournalEntryStatus.DRAFT,
            createdBy: userId,
            updatedBy: userId,
          })
          .returning();

        // Create journal entry lines
        const lineValues = createJournalEntryDto.journalEntryLines.map(
          (line) => ({
            journalId: journalEntry.id,
            businessId,
            lineNumber: line.lineNumber,
            sortOrder: line.sortOrder,
            accountId: line.accountId,
            debitAmount: line.debitAmount,
            creditAmount: line.creditAmount,
            description: line.description,
            nameReference: line.nameReference,
            taxId: line.taxId,
            entityType: line.entityType,
            entityId: line.entityId,
            createdBy: userId,
            updatedBy: userId,
          }),
        );

        await tx.insert(journalEntryLines).values(lineValues);

        return journalEntry;
      });

      // Log activity
      await this.activityLogService.logCreate(
        result.id,
        EntityType.ACCOUNT,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return this.findOneJournalEntry(userId, businessId, result.id);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create journal entry: ${error.message}`,
      );
    }
  }

  async createJournalEntryAndReturnId(
    userId: string,
    businessId: string | null,
    createJournalEntryDto: CreateJournalEntryDto,
    metadata?: ActivityMetadata,
  ): Promise<JournalEntryIdResponseDto> {
    const journalEntry = await this.createJournalEntry(
      userId,
      businessId,
      createJournalEntryDto,
      metadata,
    );
    return { id: journalEntry.id };
  }

  async findOneJournalEntry(
    _userId: string,
    businessId: string | null,
    id: string,
  ): Promise<JournalEntryDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const journalEntryResult = await this.db
        .select({
          id: journalEntries.id,
          businessId: journalEntries.businessId,
          journalNumber: journalEntries.journalNumber,
          journalDate: journalEntries.journalDate,
          description: journalEntries.description,
          memo: journalEntries.memo,
          referenceType: journalEntries.referenceType,
          referenceId: journalEntries.referenceId,
          status: journalEntries.status,
          createdBy: users.name,
          updatedBy: sql<string>`updated_user.name`,
          createdAt: journalEntries.createdAt,
          updatedAt: journalEntries.updatedAt,
        })
        .from(journalEntries)
        .leftJoin(users, eq(journalEntries.createdBy, users.id))
        .leftJoin(
          sql`${users} as updated_user`,
          eq(journalEntries.updatedBy, sql`updated_user.id`),
        )
        .where(
          and(
            eq(journalEntries.id, id),
            eq(journalEntries.businessId, businessId),
            isNull(journalEntries.isDeleted),
          ),
        )
        .then((results) => results[0]);

      if (!journalEntryResult) {
        throw new NotFoundException('Journal entry not found');
      }

      // Get journal entry lines
      const journalEntryLinesResult = await this.db
        .select({
          id: journalEntryLines.id,
          journalId: journalEntryLines.journalId,
          lineNumber: journalEntryLines.lineNumber,
          sortOrder: journalEntryLines.sortOrder,
          accountId: journalEntryLines.accountId,
          accountName: accounts.accountName,
          debitAmount: journalEntryLines.debitAmount,
          creditAmount: journalEntryLines.creditAmount,
          description: journalEntryLines.description,
          nameReference: journalEntryLines.nameReference,
          taxId: journalEntryLines.taxId,
          taxName: taxes.taxName,
          entityType: journalEntryLines.entityType,
          entityId: journalEntryLines.entityId,
          createdAt: journalEntryLines.createdAt,
          updatedAt: journalEntryLines.updatedAt,
        })
        .from(journalEntryLines)
        .leftJoin(accounts, eq(journalEntryLines.accountId, accounts.id))
        .leftJoin(taxes, eq(journalEntryLines.taxId, taxes.id))
        .where(eq(journalEntryLines.journalId, id))
        .orderBy(asc(journalEntryLines.sortOrder));

      // Calculate totals
      const totalDebitAmount = journalEntryLinesResult.reduce(
        (sum, line) => sum + parseFloat(line.debitAmount || '0'),
        0,
      );
      const totalCreditAmount = journalEntryLinesResult.reduce(
        (sum, line) => sum + parseFloat(line.creditAmount || '0'),
        0,
      );
      const isBalanced = Math.abs(totalDebitAmount - totalCreditAmount) < 0.01;

      return {
        ...journalEntryResult,
        journalEntryLines: journalEntryLinesResult,
        totalDebitAmount: totalDebitAmount.toFixed(2),
        totalCreditAmount: totalCreditAmount.toFixed(2),
        isBalanced,
      } as any;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to retrieve journal entry: ${error.message}`,
      );
    }
  }

  async findAllJournalEntriesOptimized(
    _userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    journalNumber?: string,
    description?: string,
    status?: string,
    referenceType?: string,
    _filters?: string,
    _joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: any[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(journalEntries.businessId, businessId),
      isNull(journalEntries.isDeleted),
    ];

    // Add search filters
    if (journalNumber) {
      whereConditions.push(
        like(journalEntries.journalNumber, `%${journalNumber}%`),
      );
    }

    if (description) {
      whereConditions.push(
        like(journalEntries.description, `%${description}%`),
      );
    }

    if (status) {
      whereConditions.push(eq(journalEntries.status, status as any));
    }

    if (referenceType) {
      whereConditions.push(
        eq(journalEntries.referenceType, referenceType as any),
      );
    }

    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(
          gte(journalEntries.journalDate, fromDate.toISOString().split('T')[0]),
        );
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(
          lte(journalEntries.journalDate, toDate.toISOString().split('T')[0]),
        );
      }
    }

    // Build final where clause
    const whereClause = and(...whereConditions);

    // Build order by clause
    let orderBy = [desc(journalEntries.createdAt), asc(journalEntries.id)];

    if (sort) {
      try {
        const parsedSort = JSON.parse(sort);
        if (parsedSort.length > 0) {
          const sortField = parsedSort[0];
          const isDesc = sortField.desc === true;

          switch (sortField.id) {
            case 'journalNumber':
              orderBy = [
                isDesc
                  ? desc(journalEntries.journalNumber)
                  : asc(journalEntries.journalNumber),
                asc(journalEntries.id),
              ];
              break;
            case 'journalDate':
              orderBy = [
                isDesc
                  ? desc(journalEntries.journalDate)
                  : asc(journalEntries.journalDate),
                asc(journalEntries.id),
              ];
              break;
            case 'description':
              orderBy = [
                isDesc
                  ? desc(journalEntries.description)
                  : asc(journalEntries.description),
                asc(journalEntries.id),
              ];
              break;
            case 'status':
              orderBy = [
                isDesc
                  ? desc(journalEntries.status)
                  : asc(journalEntries.status),
                asc(journalEntries.id),
              ];
              break;
          }
        }
      } catch {
        // Invalid JSON, use default sort
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(journalEntries)
      .where(whereClause);

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Get paginated results
    const results = await this.db
      .select({
        id: journalEntries.id,
        journalNumber: journalEntries.journalNumber,
        journalDate: journalEntries.journalDate,
        description: journalEntries.description,
        status: journalEntries.status,
        referenceType: journalEntries.referenceType,
        referenceId: journalEntries.referenceId,
        createdAt: journalEntries.createdAt,
        updatedAt: journalEntries.updatedAt,
      })
      .from(journalEntries)
      .where(whereClause)
      .orderBy(...orderBy)
      .limit(limit)
      .offset(offset);

    return {
      data: results,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async updateJournalEntry(
    userId: string,
    businessId: string | null,
    id: string,
    updateJournalEntryDto: UpdateJournalEntryDto,
    metadata?: ActivityMetadata,
  ): Promise<JournalEntryDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if journal entry exists
      const existingJournalEntry = await this.db
        .select()
        .from(journalEntries)
        .where(
          and(
            eq(journalEntries.id, id),
            eq(journalEntries.businessId, businessId),
            isNull(journalEntries.isDeleted),
          ),
        )
        .then((results) => results[0]);

      if (!existingJournalEntry) {
        throw new NotFoundException('Journal entry not found');
      }

      // If journal number is being updated, check uniqueness
      if (
        updateJournalEntryDto.journalNumber &&
        updateJournalEntryDto.journalNumber !==
          existingJournalEntry.journalNumber
      ) {
        const duplicateJournal = await this.db
          .select()
          .from(journalEntries)
          .where(
            and(
              eq(journalEntries.businessId, businessId),
              eq(
                journalEntries.journalNumber,
                updateJournalEntryDto.journalNumber,
              ),
              isNull(journalEntries.isDeleted),
              sql`${journalEntries.id} != ${id}`,
            ),
          )
          .then((results) => results[0]);

        if (duplicateJournal) {
          throw new ConflictException(
            `Journal number '${updateJournalEntryDto.journalNumber}' already exists`,
          );
        }
      }

      const updateData: any = {
        ...updateJournalEntryDto,
        updatedBy: userId,
        updatedAt: new Date(),
      };

      await this.db
        .update(journalEntries)
        .set(updateData)
        .where(eq(journalEntries.id, id));

      // Log activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.ACCOUNT,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return this.findOneJournalEntry(userId, businessId, id);
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to update journal entry');
    }
  }

  async updateJournalEntryAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateJournalEntryDto: UpdateJournalEntryDto,
    metadata?: ActivityMetadata,
  ): Promise<JournalEntryIdResponseDto> {
    await this.updateJournalEntry(
      userId,
      businessId,
      id,
      updateJournalEntryDto,
      metadata,
    );
    return { id };
  }

  async removeJournalEntry(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<DeleteJournalEntryResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if journal entry exists
    const existingJournalEntry = await this.db
      .select()
      .from(journalEntries)
      .where(
        and(
          eq(journalEntries.id, id),
          eq(journalEntries.businessId, businessId),
          isNull(journalEntries.isDeleted),
        ),
      )
      .then((results) => results[0]);

    if (!existingJournalEntry) {
      throw new NotFoundException('Journal entry not found');
    }

    // Soft delete the journal entry
    await this.db
      .update(journalEntries)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(journalEntries.id, id));

    // Log activity
    await this.activityLogService.logDelete(
      id,
      EntityType.ACCOUNT,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      success: true,
      message: 'Journal entry deleted successfully',
    };
  }

  async bulkCreateJournalEntries(
    userId: string,
    businessId: string | null,
    createJournalEntryDtos: CreateJournalEntryDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const createdIds: string[] = [];

    await this.db.transaction(async (tx) => {
      for (const createJournalEntryDto of createJournalEntryDtos) {
        // Validate balance
        const totalDebits = createJournalEntryDto.journalEntryLines.reduce(
          (sum, line) => sum + parseFloat(line.debitAmount),
          0,
        );
        const totalCredits = createJournalEntryDto.journalEntryLines.reduce(
          (sum, line) => sum + parseFloat(line.creditAmount),
          0,
        );

        if (Math.abs(totalDebits - totalCredits) > 0.01) {
          throw new BadRequestException(
            `Journal entry ${createJournalEntryDto.journalNumber} is not balanced`,
          );
        }

        // Check if journal number already exists
        const existingJournalEntry = await tx
          .select()
          .from(journalEntries)
          .where(
            and(
              eq(journalEntries.businessId, businessId),
              eq(
                journalEntries.journalNumber,
                createJournalEntryDto.journalNumber,
              ),
              isNull(journalEntries.isDeleted),
            ),
          )
          .then((results) => results[0]);

        if (existingJournalEntry) {
          throw new ConflictException(
            `Journal number '${createJournalEntryDto.journalNumber}' already exists`,
          );
        }

        // Create journal entry
        const [journalEntry] = await tx
          .insert(journalEntries)
          .values({
            businessId,
            journalNumber: createJournalEntryDto.journalNumber,
            journalDate: createJournalEntryDto.journalDate,
            description: createJournalEntryDto.description,
            memo: createJournalEntryDto.memo,
            referenceType: createJournalEntryDto.referenceType,
            referenceId: createJournalEntryDto.referenceId,
            status: createJournalEntryDto.status || JournalEntryStatus.DRAFT,
            createdBy: userId,
            updatedBy: userId,
          })
          .returning();

        // Create journal entry lines
        const lineValues = createJournalEntryDto.journalEntryLines.map(
          (line) => ({
            journalId: journalEntry.id,
            businessId,
            lineNumber: line.lineNumber,
            sortOrder: line.sortOrder,
            accountId: line.accountId,
            debitAmount: line.debitAmount,
            creditAmount: line.creditAmount,
            description: line.description,
            nameReference: line.nameReference,
            taxId: line.taxId,
            entityType: line.entityType,
            entityId: line.entityId,
            createdBy: userId,
            updatedBy: userId,
          }),
        );

        await tx.insert(journalEntryLines).values(lineValues);

        createdIds.push(journalEntry.id);
      }
    });

    // Log bulk create operation
    await this.activityLogService.logBulkOperation(
      ActivityType.BULK_CREATE,
      EntityType.ACCOUNT,
      createdIds,
      { bulkCreateCount: createdIds.length },
      userId,
      businessId,
      {
        filterCriteria: { bulkJournalEntriesCount: createdIds.length },
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return { ids: createdIds };
  }

  async bulkCreateJournalEntriesAndReturnIds(
    userId: string,
    businessId: string | null,
    createJournalEntryDtos: CreateJournalEntryDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    return this.bulkCreateJournalEntries(
      userId,
      businessId,
      createJournalEntryDtos,
      metadata,
    );
  }

  async bulkDeleteJournalEntries(
    userId: string,
    businessId: string | null,
    ids: string[],
    metadata?: ActivityMetadata,
  ): Promise<{ deleted: number; message: string; deletedIds: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const deletedIds: string[] = [];

    await this.db.transaction(async (tx) => {
      for (const id of ids) {
        // Check if journal entry exists
        const existingJournalEntry = await tx
          .select()
          .from(journalEntries)
          .where(
            and(
              eq(journalEntries.id, id),
              eq(journalEntries.businessId, businessId),
              isNull(journalEntries.isDeleted),
            ),
          )
          .then((results) => results[0]);

        if (existingJournalEntry) {
          await tx
            .update(journalEntries)
            .set({
              isDeleted: true,
              updatedBy: userId,
              updatedAt: new Date(),
            })
            .where(eq(journalEntries.id, id));

          deletedIds.push(id);
        }
      }
    });

    // Log bulk delete operation
    await this.activityLogService.logBulkOperation(
      ActivityType.BULK_DELETE,
      EntityType.ACCOUNT,
      deletedIds,
      { bulkDeleteCount: deletedIds.length },
      userId,
      businessId,
      {
        filterCriteria: { journalEntryIds: ids },
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      deleted: deletedIds.length,
      message: `${deletedIds.length} journal entries deleted successfully`,
      deletedIds,
    };
  }

  async checkJournalNumberAvailability(
    _userId: string,
    businessId: string | null,
    journalNumber: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingJournalEntry = await this.db
      .select()
      .from(journalEntries)
      .where(
        and(
          eq(journalEntries.businessId, businessId),
          eq(journalEntries.journalNumber, journalNumber),
          isNull(journalEntries.isDeleted),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingJournalEntry };
  }

  // Tax Account Management Methods

  /**
   * Creates a sales tax payable account for a tax
   */
  async createSalesTaxPayableAccount(
    userId: string,
    businessId: string,
    taxId: string,
    taxName: string,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const accountNumber = await this.generateTaxAccountNumber(
      businessId,
      'sales',
    );

    const salesTaxAccount = {
      accountName: `${taxName} - Sales Tax Payable`,
      accountNumber,
      accountCategory: AccountCategory.LIABILITIES,
      accountType: ChartAccountType.CURRENT_LIABILITIES,
      accountDetailType: AccountDetailType.SALES_SERVICE_TAX_PAYABLE,
      description: `Sales tax payable account for ${taxName}`,
      defaultTaxId: taxId,
      isSystemAccount: false,
      status: AccountStatus.ACTIVE,
    };

    return this.create(userId, businessId, salesTaxAccount, metadata);
  }

  /**
   * Creates a purchase tax receivable account for a tax
   */
  async createPurchaseTaxReceivableAccount(
    userId: string,
    businessId: string,
    taxId: string,
    taxName: string,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const accountNumber = await this.generateTaxAccountNumber(
      businessId,
      'purchase',
    );

    const purchaseTaxAccount = {
      accountName: `${taxName} - Purchase Tax Receivable`,
      accountNumber,
      accountCategory: AccountCategory.ASSETS,
      accountType: ChartAccountType.CURRENT_ASSETS,
      accountDetailType: AccountDetailType.OTHER_CURRENT_ASSETS,
      description: `Purchase tax receivable account for ${taxName}`,
      defaultTaxId: taxId,
      isSystemAccount: false,
      status: AccountStatus.ACTIVE,
    };

    return this.create(userId, businessId, purchaseTaxAccount, metadata);
  }

  /**
   * Creates a purchase tax expense account for a tax
   */
  async createPurchaseTaxExpenseAccount(
    userId: string,
    businessId: string,
    taxId: string,
    taxName: string,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const accountNumber = await this.generateTaxAccountNumber(
      businessId,
      'expense',
    );

    const purchaseTaxExpenseAccount = {
      accountName: `${taxName} - Tax Expense`,
      accountNumber,
      accountCategory: AccountCategory.EXPENSES,
      accountType: ChartAccountType.EXPENSES,
      accountDetailType: AccountDetailType.TAXES_PAID,
      description: `Non-reclaimable tax expense account for ${taxName}`,
      defaultTaxId: taxId,
      isSystemAccount: false,
      status: AccountStatus.ACTIVE,
    };

    return this.create(userId, businessId, purchaseTaxExpenseAccount, metadata);
  }

  /**
   * Generates appropriate account numbers for tax accounts
   */
  private async generateTaxAccountNumber(
    businessId: string,
    type: 'sales' | 'purchase' | 'expense',
  ): Promise<string> {
    const prefixMap = {
      sales: '2210', // Current Liabilities range
      purchase: '1310', // Current Assets range
      expense: '5210', // Expense range
    };

    const prefix = prefixMap[type];

    // Find the last account number with this prefix
    const lastAccounts = await this.db
      .select({ accountNumber: accounts.accountNumber })
      .from(accounts)
      .where(
        and(
          eq(accounts.businessId, businessId),
          like(accounts.accountNumber, `${prefix}%`),
          eq(accounts.isDeleted, false),
        ),
      )
      .orderBy(desc(accounts.accountNumber))
      .limit(1);

    if (!lastAccounts.length) {
      return `${prefix}001`;
    }

    // Extract the numeric part and increment
    const lastAccountNumber = lastAccounts[0].accountNumber;
    if (!lastAccountNumber || !lastAccountNumber.startsWith(prefix)) {
      return `${prefix}001`;
    }

    const numericPart = lastAccountNumber.substring(prefix.length);
    const nextNumber = parseInt(numericPart) + 1;
    return `${prefix}${nextNumber.toString().padStart(3, '0')}`;
  }

  /**
   * Deactivates tax-related accounts
   */
  async deactivateTaxAccount(
    userId: string,
    businessId: string,
    accountId: string,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    return this.update(
      userId,
      businessId,
      accountId,
      { status: AccountStatus.INACTIVE },
      metadata,
    );
  }

  /**
   * Finds accounts associated with a specific tax
   */
  async findAccountsByTaxId(
    businessId: string,
    taxId: string,
  ): Promise<AccountSlimDto[]> {
    const results = await this.db
      .select({
        id: accounts.id,
        accountName: accounts.accountName,
        accountNumber: accounts.accountNumber,
        accountCategory: accounts.accountCategory,
        accountType: accounts.accountType,
        status: accounts.status,
      })
      .from(accounts)
      .where(
        and(
          eq(accounts.businessId, businessId),
          eq(accounts.defaultTaxId, taxId),
          eq(accounts.isDeleted, false),
        ),
      )
      .orderBy(asc(accounts.accountName));

    return results as AccountSlimDto[];
  }
}

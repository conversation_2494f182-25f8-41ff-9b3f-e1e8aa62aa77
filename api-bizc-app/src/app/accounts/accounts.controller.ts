import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
  BadRequestException,
} from '@nestjs/common';
import { AccountsService } from './accounts.service';
import { CreateJournalEntryDto } from '../journal-entries/dto/create-journal-entry.dto';
import { UpdateJournalEntryDto } from '../journal-entries/dto/update-journal-entry.dto';
import { JournalEntryIdResponseDto } from '../journal-entries/dto/journal-entry-id-response.dto';
import { BulkCreateJournalEntryDto } from '../journal-entries/dto/bulk-create-journal-entry.dto';
import { BulkJournalEntryIdsResponseDto } from '../journal-entries/dto/bulk-journal-entry-ids-response.dto';
import { BulkDeleteJournalEntryDto } from '../journal-entries/dto/bulk-delete-journal-entry.dto';
import { BulkDeleteJournalEntryResponseDto } from '../journal-entries/dto/bulk-delete-journal-entry-response.dto';
import { DeleteJournalEntryResponseDto } from '../journal-entries/dto/delete-journal-entry-response.dto';
import { PaginatedJournalEntriesResponseDto } from '../journal-entries/dto/paginated-journal-entries-response.dto';
import { JournalEntryNumberAvailabilityResponseDto } from '../journal-entries/dto/check-journal-entry-number.dto';
import { CreateAccountDto } from './dto/create-account.dto';
import { BulkCreateAccountDto } from './dto/bulk-create-account.dto';
import { UpdateAccountDto } from './dto/update-account.dto';
import { AccountDto } from './dto/account.dto';
import { AccountSlimDto } from './dto/account-slim.dto';
import { AccountIdResponseDto } from './dto/account-id-response.dto';
import { PaginatedAccountsResponseDto } from './dto/paginated-accounts-response.dto';
import { BulkAccountIdsResponseDto } from './dto/bulk-account-ids-response.dto';
import { BulkDeleteAccountDto } from './dto/bulk-delete-account.dto';
import { BulkDeleteAccountResponseDto } from './dto/bulk-delete-account-response.dto';
import { DeleteAccountResponseDto } from './dto/delete-account-response.dto';
import { FilteredAccountsResponseDto } from './dto/filtered-accounts-response.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { AccountNameAvailabilityResponseDto } from './dto/check-account-name.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission } from '../shared/types/permission.enum';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

@ApiTags('accounts')
@Controller('accounts')
@UseGuards(PermissionsGuard)
export class AccountsController {
  constructor(private readonly accountsService: AccountsService) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_CREATE)
  @ApiOperation({ summary: 'Create a new account' })
  @ApiResponse({
    status: 201,
    description: 'The account has been successfully created',
    type: AccountIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Account name or number already exists',
  })
  create(
    @Request() req,
    @Body() createAccountDto: CreateAccountDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<AccountIdResponseDto> {
    return this.accountsService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createAccountDto,
      metadata,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_CREATE)
  @ApiOperation({ summary: 'Bulk create accounts' })
  @ApiResponse({
    status: 201,
    description: 'The accounts have been successfully created',
    type: BulkAccountIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or duplicate names',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Account names or numbers already exist',
  })
  bulkCreate(
    @Request() req,
    @Body() bulkCreateAccountDto: BulkCreateAccountDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkAccountIdsResponseDto> {
    return this.accountsService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateAccountDto.accounts,
      metadata,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_READ)
  @ApiOperation({
    summary: 'Get all accounts for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Start date for filtering (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'to',
    description: 'End date for filtering (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'accountName',
    description: 'Filter by account name',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'accountNumber',
    description: 'Filter by account number',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'accountCategory',
    description: 'Filter by account category',
    required: false,
    enum: ['ASSETS', 'LIABILITIES', 'EQUITY', 'REVENUE', 'EXPENSES'],
  })
  @ApiQuery({
    name: 'accountType',
    description: 'Filter by account type',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'accountDetailType',
    description: 'Filter by account detail type',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by status',
    required: false,
    enum: ['active', 'inactive'],
  })
  @ApiQuery({
    name: 'filters',
    description:
      'Advanced filters as JSON string with operator support. Supported operators: iLike (contains), notILike (does not contain), eq (is), ne (is not), isEmpty (is empty), isNotEmpty (is not empty)',
    required: false,
    type: String,
    example:
      '[{"id":"accountName","value":"Cash","operator":"iLike","type":"text","rowId":"1"},{"id":"status","value":"active","operator":"eq","type":"select","rowId":"2"},{"id":"accountCategory","value":"ASSETS","operator":"eq","type":"select","rowId":"3"}]',
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for advanced filters',
    required: false,
    type: String,
    enum: ['and', 'or'],
    example: 'and',
  })
  @ApiQuery({
    name: 'sort',
    description:
      'Sort configuration as JSON string. Supported fields: accountName, accountNumber, accountCategory, accountType, openingBalance, createdAt, updatedAt',
    required: false,
    type: String,
    example: '[{"id":"accountName","desc":false}]',
  })
  @ApiResponse({
    status: 200,
    description: 'Accounts returned successfully',
    type: PaginatedAccountsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('accountName') accountName?: string,
    @Query('accountNumber') accountNumber?: string,
    @Query('accountCategory') accountCategory?: string,
    @Query('accountType') accountType?: string,
    @Query('accountDetailType') accountDetailType?: string,
    @Query('status') status?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedAccountsResponseDto> {
    return this.accountsService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page ? parseInt(page.toString()) : undefined,
      limit ? parseInt(limit.toString()) : undefined,
      from,
      to,
      accountName,
      accountNumber,
      accountCategory,
      accountType,
      accountDetailType,
      status,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('filter')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_READ)
  @ApiOperation({
    summary: 'Filter accounts by category, type, and/or detail type',
    description:
      'Get accounts filtered by accountCategory, accountType, and/or accountDetailType. All parameters are optional and can be used in any combination.',
  })
  @ApiQuery({
    name: 'accountCategory',
    description: 'Filter by account category',
    required: false,
    enum: ['ASSETS', 'LIABILITIES', 'EQUITY', 'REVENUE', 'EXPENSES'],
  })
  @ApiQuery({
    name: 'accountType',
    description: 'Filter by account type',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'accountDetailType',
    description: 'Filter by account detail type',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Filtered accounts returned successfully',
    type: FilteredAccountsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findFilteredAccounts(
    @Request() req,
    @Query('accountCategory') accountCategory?: string,
    @Query('accountType') accountType?: string,
    @Query('accountDetailType') accountDetailType?: string,
  ): Promise<FilteredAccountsResponseDto> {
    return this.accountsService
      .findFilteredAccounts(
        req.user.id,
        req.user.activeBusinessId,
        accountCategory,
        accountType,
        accountDetailType,
      )
      .then((data) => ({
        data,
        message: 'Accounts filtered successfully',
      }));
  }

  @Get('check-name-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_READ)
  @ApiOperation({
    summary: 'Check if an account name is available for the business',
  })
  @ApiQuery({
    name: 'accountName',
    description: 'Account name to check',
    required: true,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Account name availability checked successfully',
    type: AccountNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkAccountNameAvailability(
    @Request() req,
    @Query('accountName') accountName: string,
  ): Promise<{ available: boolean }> {
    return this.accountsService.checkAccountNameAvailability(
      req.user.id,
      req.user.activeBusinessId,
      accountName,
    );
  }

  @Get('check-number-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_READ)
  @ApiOperation({
    summary: 'Check if an account number is available for the business',
  })
  @ApiQuery({
    name: 'accountNumber',
    description: 'Account number to check',
    required: true,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Account number availability checked successfully',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkAccountNumberAvailability(
    @Request() req,
    @Query('accountNumber') accountNumber: string,
  ): Promise<{ available: boolean }> {
    return this.accountsService.checkAccountNumberAvailability(
      req.user.id,
      req.user.activeBusinessId,
      accountNumber,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_READ)
  @ApiOperation({ summary: 'Get all accounts in slim format' })
  @ApiResponse({
    status: 200,
    description: 'All accounts returned successfully',
    type: [AccountSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req): Promise<AccountSlimDto[]> {
    return this.accountsService.findAllSlim(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_READ)
  @ApiOperation({ summary: 'Get account by ID' })
  @ApiParam({
    name: 'id',
    description: 'Account ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Account returned successfully',
    type: AccountDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Account not found',
  })
  findOne(@Request() req, @Param('id') id: string): Promise<AccountDto> {
    return this.accountsService.findOne(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_UPDATE)
  @ApiOperation({ summary: 'Update account by ID' })
  @ApiParam({
    name: 'id',
    description: 'Account ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Account updated successfully',
    type: AccountIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Account not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Account name or number already exists',
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateAccountDto: UpdateAccountDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<AccountIdResponseDto> {
    return this.accountsService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateAccountDto,
      metadata,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_DELETE)
  @ApiOperation({ summary: 'Bulk delete accounts' })
  @ApiBody({
    description: 'Array of account IDs to delete',
    type: BulkDeleteAccountDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Accounts deleted successfully',
    type: BulkDeleteAccountResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  bulkDelete(
    @Request() req,
    @Body() bulkDeleteAccountDto: BulkDeleteAccountDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteAccountResponseDto> {
    return this.accountsService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteAccountDto.ids,
      metadata,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_DELETE)
  @ApiOperation({ summary: 'Delete account by ID' })
  @ApiParam({
    name: 'id',
    description: 'Account ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Account deleted successfully',
    type: DeleteAccountResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Account not found',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteAccountResponseDto> {
    return this.accountsService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }

  @Get(':id/balance')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_READ)
  @ApiOperation({ summary: 'Get account balance' })
  @ApiParam({
    name: 'id',
    description: 'Account ID',
    type: String,
  })
  @ApiQuery({
    name: 'asOfDate',
    description: 'As of date for balance calculation (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Account balance retrieved successfully',
  })
  async getAccountBalance(
    @Request() req,
    @Param('id') id: string,
    @Query('asOfDate') asOfDate?: string,
  ) {
    const cutoffDate = asOfDate ? new Date(asOfDate) : undefined;
    return this.accountsService.getAccountBalance(
      req.user.activeBusinessId,
      id,
      cutoffDate,
    );
  }

  @Get(':id/history')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_READ)
  @ApiOperation({ summary: 'Get account transaction history' })
  @ApiParam({
    name: 'id',
    description: 'Account ID',
    type: String,
  })
  @ApiQuery({
    name: 'startDate',
    description: 'Start date for history (YYYY-MM-DD)',
    required: true,
    type: String,
  })
  @ApiQuery({
    name: 'endDate',
    description: 'End date for history (YYYY-MM-DD)',
    required: true,
    type: String,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of transactions to return',
    required: false,
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'Account history retrieved successfully',
  })
  async getAccountHistory(
    @Request() req,
    @Param('id') id: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query('limit') limit?: number,
  ) {
    return this.accountsService.getAccountHistory(
      req.user.activeBusinessId,
      id,
      new Date(startDate),
      new Date(endDate),
      limit || 100,
    );
  }

  // Journal Entries Endpoints
  @Get('journal-entries')
  @ApiBearerAuth()
  @RequirePermissions(Permission.JOURNAL_ENTRY_READ)
  @ApiOperation({ summary: 'Get journal entries with accounting context' })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'accountId',
    description: 'Filter by account ID',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'startDate',
    description: 'Start date for filtering (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'endDate',
    description: 'End date for filtering (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Journal entries retrieved successfully',
  })
  async findAllJournalEntries(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('accountId') accountId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    let filters: string | undefined;
    if (accountId) {
      filters = JSON.stringify([
        {
          field: 'accountId',
          operator: 'equals',
          value: accountId,
        },
      ]);
    }

    return this.accountsService.findAllJournalEntriesOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page,
      limit,
      startDate,
      endDate,
      undefined,
      undefined,
      undefined,
      undefined,
      filters,
      undefined,
      undefined,
    );
  }

  @Get('journal-entries/:id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.JOURNAL_ENTRY_READ)
  @ApiOperation({ summary: 'Get journal entry by ID with balance impact' })
  @ApiResponse({
    status: 200,
    description: 'Journal entry retrieved successfully',
  })
  async findOneJournalEntry(@Request() req, @Param('id') id: string) {
    try {
      const journalEntry = await this.accountsService.findOneJournalEntry(
        req.user.id,
        req.user.activeBusinessId,
        id,
      );

      if (
        journalEntry.journalEntryLines &&
        journalEntry.journalEntryLines.length > 0
      ) {
        const balanceImpacts = await Promise.all(
          journalEntry.journalEntryLines.map(async (line) => {
            try {
              const balance = await this.accountsService.getAccountBalance(
                req.user.activeBusinessId,
                line.accountId,
                new Date(journalEntry.journalDate),
              );
              const debitAmount = parseFloat(line.debitAmount || '0');
              const creditAmount = parseFloat(line.creditAmount || '0');
              const impact = debitAmount - creditAmount;

              return {
                accountId: line.accountId,
                balanceBeforeEntry: balance.netBalance - impact,
                balanceAfterEntry: balance.netBalance,
                impact,
              };
            } catch {
              return {
                accountId: line.accountId,
                balanceBeforeEntry: 0,
                balanceAfterEntry: 0,
                impact:
                  parseFloat(line.debitAmount || '0') -
                  parseFloat(line.creditAmount || '0'),
                error: 'Could not calculate balance',
              };
            }
          }),
        );

        return {
          ...journalEntry,
          balanceImpacts,
        };
      }

      return journalEntry;
    } catch (error) {
      throw new BadRequestException(
        `Error retrieving journal entry: ${error.message}`,
      );
    }
  }

  @Get('journal-entries/:id/balance-impact')
  @ApiBearerAuth()
  @RequirePermissions(Permission.JOURNAL_ENTRY_READ)
  @ApiOperation({ summary: 'Get balance impact of a journal entry' })
  @ApiResponse({
    status: 200,
    description: 'Balance impact calculated successfully',
  })
  async getJournalEntryBalanceImpact(@Request() req, @Param('id') id: string) {
    try {
      const journalEntry = await this.accountsService.findOneJournalEntry(
        req.user.id,
        req.user.activeBusinessId,
        id,
      );

      if (
        !journalEntry.journalEntryLines ||
        journalEntry.journalEntryLines.length === 0
      ) {
        return {
          journalEntryId: id,
          journalDate: journalEntry.journalDate,
          description: journalEntry.description,
          impacts: [],
          totalDebits: 0,
          totalCredits: 0,
        };
      }

      const impacts = await Promise.all(
        journalEntry.journalEntryLines.map(async (line) => {
          try {
            const [currentBalance, balanceAsOfEntry] = await Promise.all([
              this.accountsService.getAccountBalance(
                req.user.activeBusinessId,
                line.accountId,
                new Date(),
              ),
              this.accountsService.getAccountBalance(
                req.user.activeBusinessId,
                line.accountId,
                new Date(journalEntry.journalDate),
              ),
            ]);

            const debitAmount = parseFloat(line.debitAmount || '0');
            const creditAmount = parseFloat(line.creditAmount || '0');

            return {
              accountId: line.accountId,
              accountName: line.accountName || 'Unknown Account',
              debitAmount,
              creditAmount,
              netImpact: debitAmount - creditAmount,
              balanceAsOfEntry: balanceAsOfEntry.netBalance,
              currentBalance: currentBalance.netBalance,
            };
          } catch {
            const debitAmount = parseFloat(line.debitAmount || '0');
            const creditAmount = parseFloat(line.creditAmount || '0');

            return {
              accountId: line.accountId,
              accountName: line.accountName || 'Unknown Account',
              debitAmount,
              creditAmount,
              netImpact: debitAmount - creditAmount,
              balanceAsOfEntry: 0,
              currentBalance: 0,
              error: 'Could not calculate balance',
            };
          }
        }),
      );

      return {
        journalEntryId: id,
        journalDate: journalEntry.journalDate,
        description: journalEntry.description,
        impacts,
        totalDebits: impacts.reduce(
          (sum, impact) => sum + impact.debitAmount,
          0,
        ),
        totalCredits: impacts.reduce(
          (sum, impact) => sum + impact.creditAmount,
          0,
        ),
      };
    } catch (error) {
      throw new BadRequestException(
        `Error calculating balance impact: ${error.message}`,
      );
    }
  }

  @Post('journal-entries/validate-balance')
  @ApiBearerAuth()
  @RequirePermissions(Permission.JOURNAL_ENTRY_CREATE)
  @ApiOperation({ summary: 'Validate journal entry balance before creation' })
  @ApiBody({
    description: 'Journal entry data to validate',
    schema: {
      type: 'object',
      properties: {
        journalEntryLines: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              debitAmount: { type: 'string', example: '100.00' },
              creditAmount: { type: 'string', example: '0.00' },
              accountId: { type: 'string', format: 'uuid' },
            },
          },
        },
        lines: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              debitAmount: { type: 'string', example: '100.00' },
              creditAmount: { type: 'string', example: '0.00' },
              accountId: { type: 'string', format: 'uuid' },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Journal entry validation result',
    schema: {
      type: 'object',
      properties: {
        isBalanced: { type: 'boolean' },
        totalDebits: { type: 'number' },
        totalCredits: { type: 'number' },
        difference: { type: 'number' },
        message: { type: 'string' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Invalid request data' })
  async validateJournalEntryBalance(
    @Request() req,
    @Body() journalEntryData: any,
  ) {
    if (!journalEntryData || typeof journalEntryData !== 'object') {
      throw new BadRequestException('Invalid journal entry data provided');
    }

    const lines =
      journalEntryData.journalEntryLines || journalEntryData.lines || [];

    if (!Array.isArray(lines)) {
      throw new BadRequestException('Journal entry lines must be an array');
    }

    if (lines.length === 0) {
      throw new BadRequestException(
        'Journal entry must have at least one line',
      );
    }

    try {
      const totalDebits = lines.reduce((sum: number, line: any) => {
        const debitAmount = parseFloat(line.debitAmount || '0');
        if (isNaN(debitAmount)) {
          throw new BadRequestException(
            `Invalid debit amount: ${line.debitAmount}`,
          );
        }
        return sum + debitAmount;
      }, 0);

      const totalCredits = lines.reduce((sum: number, line: any) => {
        const creditAmount = parseFloat(line.creditAmount || '0');
        if (isNaN(creditAmount)) {
          throw new BadRequestException(
            `Invalid credit amount: ${line.creditAmount}`,
          );
        }
        return sum + creditAmount;
      }, 0);

      const isBalanced = Math.abs(totalDebits - totalCredits) < 0.01;
      const difference = totalDebits - totalCredits;

      return {
        isBalanced,
        totalDebits: Math.round(totalDebits * 100) / 100,
        totalCredits: Math.round(totalCredits * 100) / 100,
        difference: Math.round(difference * 100) / 100,
        message: isBalanced
          ? 'Journal entry is balanced'
          : `Journal entry is not balanced. Difference: ${difference.toFixed(2)}`,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Error validating journal entry balance');
    }
  }

  // Reports Endpoints
  @Get('reports/trial-balance')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_READ)
  @ApiOperation({ summary: 'Get trial balance report' })
  @ApiQuery({
    name: 'asOfDate',
    description: 'As of date for the report (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Trial balance report generated successfully',
  })
  async getTrialBalance(@Request() req, @Query('asOfDate') asOfDate?: string) {
    const cutoffDate = asOfDate ? new Date(asOfDate) : undefined;
    return this.accountsService.getTrialBalance(
      req.user.activeBusinessId,
      cutoffDate,
    );
  }

  @Get('reports/balance-sheet')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_READ)
  @ApiOperation({ summary: 'Get balance sheet report' })
  @ApiQuery({
    name: 'asOfDate',
    description: 'As of date for the report (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Balance sheet report generated successfully',
  })
  async getBalanceSheet(@Request() req, @Query('asOfDate') asOfDate?: string) {
    const cutoffDate = asOfDate ? new Date(asOfDate) : undefined;
    return this.accountsService.getBalanceSheet(
      req.user.activeBusinessId,
      cutoffDate,
    );
  }

  @Get('reports/income-statement')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_READ)
  @ApiOperation({ summary: 'Get income statement (P&L) report' })
  @ApiQuery({
    name: 'startDate',
    description: 'Start date for the report (YYYY-MM-DD)',
    required: true,
    type: String,
  })
  @ApiQuery({
    name: 'endDate',
    description: 'End date for the report (YYYY-MM-DD)',
    required: true,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Income statement report generated successfully',
  })
  async getIncomeStatement(
    @Request() req,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    return this.accountsService.getIncomeStatement(
      req.user.activeBusinessId,
      new Date(startDate),
      new Date(endDate),
    );
  }

  @Get('reports/account-summary')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_READ)
  @ApiOperation({ summary: 'Get account summary with totals by category' })
  @ApiQuery({
    name: 'asOfDate',
    description: 'As of date for the report (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Account summary generated successfully',
  })
  async getAccountSummary(
    @Request() req,
    @Query('asOfDate') asOfDate?: string,
  ) {
    const cutoffDate = asOfDate ? new Date(asOfDate) : undefined;
    return this.accountsService.getAccountSummary(
      req.user.activeBusinessId,
      cutoffDate,
    );
  }

  @Get('reports/account-balance/:accountId')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_READ)
  @ApiOperation({ summary: 'Get individual account balance' })
  @ApiQuery({
    name: 'asOfDate',
    description: 'As of date for the balance (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Account balance retrieved successfully',
  })
  async getReportAccountBalance(
    @Request() req,
    @Param('accountId') accountId: string,
    @Query('asOfDate') asOfDate?: string,
  ) {
    const cutoffDate = asOfDate ? new Date(asOfDate) : undefined;
    return this.accountsService.getAccountBalance(
      req.user.activeBusinessId,
      accountId,
      cutoffDate,
    );
  }

  @Get('reports/account-history/:accountId')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOUNT_READ)
  @ApiOperation({ summary: 'Get account transaction history' })
  @ApiQuery({
    name: 'startDate',
    description: 'Start date for the history (YYYY-MM-DD)',
    required: true,
    type: String,
  })
  @ApiQuery({
    name: 'endDate',
    description: 'End date for the history (YYYY-MM-DD)',
    required: true,
    type: String,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of transactions to return',
    required: false,
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'Account history retrieved successfully',
  })
  async getReportAccountHistory(
    @Request() req,
    @Param('accountId') accountId: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query('limit') limit?: number,
  ) {
    return this.accountsService.getAccountHistory(
      req.user.activeBusinessId,
      accountId,
      new Date(startDate),
      new Date(endDate),
      limit || 100,
    );
  }

  // Full Journal Entries CRUD Operations
  @Post('journal-entries/create')
  @ApiBearerAuth()
  @RequirePermissions(Permission.JOURNAL_ENTRY_CREATE)
  @ApiOperation({
    summary: 'Create a new journal entry',
    description:
      'Creates a new journal entry with its associated journal entry lines',
  })
  @ApiResponse({
    status: 201,
    description: 'Journal entry created successfully',
    type: JournalEntryIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data or unbalanced journal entry',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Journal number already exists',
  })
  createJournalEntry(
    @Request() req,
    @Body() createJournalEntryDto: CreateJournalEntryDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<JournalEntryIdResponseDto> {
    return this.accountsService.createJournalEntryAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createJournalEntryDto,
      metadata,
    );
  }

  @Post('journal-entries/bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.JOURNAL_ENTRY_CREATE)
  @ApiOperation({
    summary: 'Bulk create journal entries',
    description: 'Creates multiple journal entries in a single transaction',
  })
  @ApiBody({
    description: 'Bulk create journal entries data',
    type: BulkCreateJournalEntryDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Journal entries created successfully',
    type: BulkJournalEntryIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Journal numbers already exist',
  })
  bulkCreateJournalEntries(
    @Request() req,
    @Body() bulkCreateJournalEntryDto: BulkCreateJournalEntryDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkJournalEntryIdsResponseDto> {
    return this.accountsService.bulkCreateJournalEntriesAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateJournalEntryDto.journalEntries,
      metadata,
    );
  }

  @Get('journal-entries/all')
  @ApiBearerAuth()
  @RequirePermissions(Permission.JOURNAL_ENTRY_READ)
  @ApiOperation({
    summary: 'Get all journal entries for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Start date filter (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'to',
    description: 'End date filter (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'journalNumber',
    description: 'Filter by journal number',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'description',
    description: 'Filter by description',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by status',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'referenceType',
    description: 'Filter by reference type',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'filters',
    description: 'Advanced filters as JSON string',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for filters (and/or)',
    required: false,
    enum: ['and', 'or'],
  })
  @ApiQuery({
    name: 'sort',
    description: 'Sort field and direction (field:asc/desc)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Journal entries returned successfully',
    type: PaginatedJournalEntriesResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllJournalEntriesComplete(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('journalNumber') journalNumber?: string,
    @Query('description') description?: string,
    @Query('status') status?: string,
    @Query('referenceType') referenceType?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedJournalEntriesResponseDto> {
    return this.accountsService.findAllJournalEntriesOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page,
      limit,
      from,
      to,
      journalNumber,
      description,
      status,
      referenceType,
      filters,
      joinOperator,
      sort,
    );
  }

  @Patch('journal-entries/:id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.JOURNAL_ENTRY_UPDATE)
  @ApiOperation({ summary: 'Update journal entry by ID' })
  @ApiParam({
    name: 'id',
    description: 'Journal Entry ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Journal entry updated successfully',
    type: JournalEntryIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Journal entry not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Journal number already exists',
  })
  updateJournalEntry(
    @Request() req,
    @Param('id') id: string,
    @Body() updateJournalEntryDto: UpdateJournalEntryDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<JournalEntryIdResponseDto> {
    return this.accountsService.updateJournalEntryAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateJournalEntryDto,
      metadata,
    );
  }

  @Delete('journal-entries/bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.JOURNAL_ENTRY_DELETE)
  @ApiOperation({ summary: 'Bulk delete journal entries' })
  @ApiBody({
    description: 'Array of journal entry IDs to delete',
    type: BulkDeleteJournalEntryDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Journal entries deleted successfully',
    type: BulkDeleteJournalEntryResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  bulkDeleteJournalEntries(
    @Request() req,
    @Body() bulkDeleteJournalEntryDto: BulkDeleteJournalEntryDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteJournalEntryResponseDto> {
    return this.accountsService.bulkDeleteJournalEntries(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteJournalEntryDto.ids,
      metadata,
    );
  }

  @Delete('journal-entries/:id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.JOURNAL_ENTRY_DELETE)
  @ApiOperation({ summary: 'Delete journal entry by ID' })
  @ApiParam({
    name: 'id',
    description: 'Journal Entry ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Journal entry deleted successfully',
    type: DeleteJournalEntryResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Journal entry not found',
  })
  removeJournalEntry(
    @Request() req,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteJournalEntryResponseDto> {
    return this.accountsService.removeJournalEntry(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }

  @Get('journal-entries/check-number-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.JOURNAL_ENTRY_READ)
  @ApiOperation({
    summary: 'Check if a journal entry number is available for the business',
  })
  @ApiQuery({
    name: 'journalNumber',
    description: 'Journal number to check',
    required: true,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Journal number availability checked successfully',
    type: JournalEntryNumberAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkJournalNumberAvailability(
    @Request() req,
    @Query('journalNumber') journalNumber: string,
  ): Promise<{ available: boolean }> {
    return this.accountsService.checkJournalNumberAvailability(
      req.user.id,
      req.user.activeBusinessId,
      journalNumber,
    );
  }
}

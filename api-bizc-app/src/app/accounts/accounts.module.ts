import { Module, forwardRef } from '@nestjs/common';
import { AccountsService } from './accounts.service';
import { AccountsController } from './accounts.controller';
import { AuthModule } from '../auth/auth.module';
import { DrizzleModule } from '../drizzle/drizzle.module';
import { ActivityLogModule } from '../activity-log/activity-log.module';

// External Module Dependencies
import { TaxesModule } from '../taxes/taxes.module';

@Module({
  imports: [
    AuthModule,
    DrizzleModule,
    ActivityLogModule,
    forwardRef(() => TaxesModule),
  ],
  controllers: [AccountsController],
  providers: [AccountsService],
  exports: [AccountsService],
})
export class AccountsModule {}

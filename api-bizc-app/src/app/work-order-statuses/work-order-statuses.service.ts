import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateWorkOrderStatusDto } from './dto/create-work-order-status.dto';
import { UpdateWorkOrderStatusDto } from './dto/update-work-order-status.dto';
import { WorkOrderStatusDto } from './dto/work-order-status.dto';
import { WorkOrderStatusSlimDto } from './dto/work-order-status-slim.dto';
import { WorkOrderStatusListDto } from './dto/work-order-status-list.dto';
import { WorkOrderStatusPositionDto } from './dto/work-order-status-position.dto';
import { workOrderStatuses } from '../drizzle/schema/work-order-statuses.schema';
import { users } from '../drizzle/schema/users.schema';
import { and, eq, desc, asc, ilike, inArray, gte, lte, sql } from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { ActivityLogName } from '../shared/types';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class WorkOrderStatusesService {
  constructor(
    @Inject(DRIZZLE) private readonly db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createWorkOrderStatusDto: CreateWorkOrderStatusDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a work order status with the same status code already exists for this business
      const existingByCode = await this.db
        .select()
        .from(workOrderStatuses)
        .where(
          and(
            eq(workOrderStatuses.businessId, businessId),
            ilike(
              workOrderStatuses.statusCode,
              createWorkOrderStatusDto.statusCode,
            ),
            eq(workOrderStatuses.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingByCode) {
        throw new ConflictException(
          `Work order status with code "${createWorkOrderStatusDto.statusCode}" already exists`,
        );
      }

      // Check if a work order status with the same status name already exists for this business
      const existingByName = await this.db
        .select()
        .from(workOrderStatuses)
        .where(
          and(
            eq(workOrderStatuses.businessId, businessId),
            ilike(
              workOrderStatuses.statusName,
              createWorkOrderStatusDto.statusName,
            ),
            eq(workOrderStatuses.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingByName) {
        throw new ConflictException(
          `Work order status with name "${createWorkOrderStatusDto.statusName}" already exists`,
        );
      }

      // Get the next position
      const maxPositionResult = await this.db
        .select({
          maxPosition: sql<number>`COALESCE(MAX(${workOrderStatuses.position}), 0)`,
        })
        .from(workOrderStatuses)
        .where(
          and(
            eq(workOrderStatuses.businessId, businessId),
            eq(workOrderStatuses.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      const nextPosition = (maxPositionResult?.maxPosition || 0) + 1;

      // Create the work order status
      const [newWorkOrderStatus] = await this.db
        .insert(workOrderStatuses)
        .values({
          businessId,
          statusCode: createWorkOrderStatusDto.statusCode,
          statusName: createWorkOrderStatusDto.statusName,
          description: createWorkOrderStatusDto.description,
          colorCode: createWorkOrderStatusDto.colorCode,
          iconName: createWorkOrderStatusDto.iconName,
          statusType: createWorkOrderStatusDto.statusType,
          isActive: createWorkOrderStatusDto.isActive ?? true,
          isDefault: createWorkOrderStatusDto.isDefault ?? false,
          position: nextPosition,
          createdBy: userId,
        })
        .returning({ id: workOrderStatuses.id });

      // Log the activity
      await this.activityLogService.log(
        ActivityLogName.CREATE,
        `Work order status "${createWorkOrderStatusDto.statusName}" was created`,
        { id: newWorkOrderStatus.id, type: 'work-order-status' },
        { id: userId, type: 'user' },
        { workOrderStatusId: newWorkOrderStatus.id, businessId },
        metadata,
      );

      return {
        id: newWorkOrderStatus.id,
        message: 'Work order status created successfully',
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create work order status: ${error.message}`,
      );
    }
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    statusCode?: string,
    statusName?: string,
    statusType?: string,
    isActive?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: WorkOrderStatusListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(workOrderStatuses.isDeleted, false),
      eq(workOrderStatuses.businessId, businessId),
    ];

    // Add date range filter
    if (from && to) {
      whereConditions.push(
        and(
          gte(workOrderStatuses.createdAt, new Date(from)),
          lte(workOrderStatuses.createdAt, new Date(to)),
        ),
      );
    }

    // Add individual filters
    if (statusCode) {
      whereConditions.push(
        ilike(workOrderStatuses.statusCode, `%${statusCode}%`),
      );
    }

    if (statusName) {
      whereConditions.push(
        ilike(workOrderStatuses.statusName, `%${statusName}%`),
      );
    }

    if (statusType) {
      whereConditions.push(eq(workOrderStatuses.statusType, statusType as any));
    }

    if (isActive !== undefined) {
      const activeValue = isActive.toLowerCase() === 'true';
      whereConditions.push(eq(workOrderStatuses.isActive, activeValue));
    }

    // Build the final where clause
    const whereClause = and(...whereConditions);

    // Build order by clause
    let orderByClause;
    if (sort) {
      const [field, direction] = sort.split(':');
      const isDesc = direction?.toLowerCase() === 'desc';

      switch (field) {
        case 'statusCode':
          orderByClause = isDesc
            ? desc(workOrderStatuses.statusCode)
            : asc(workOrderStatuses.statusCode);
          break;
        case 'statusName':
          orderByClause = isDesc
            ? desc(workOrderStatuses.statusName)
            : asc(workOrderStatuses.statusName);
          break;
        case 'statusType':
          orderByClause = isDesc
            ? desc(workOrderStatuses.statusType)
            : asc(workOrderStatuses.statusType);
          break;
        case 'position':
          orderByClause = isDesc
            ? desc(workOrderStatuses.position)
            : asc(workOrderStatuses.position);
          break;
        case 'createdAt':
          orderByClause = isDesc
            ? desc(workOrderStatuses.createdAt)
            : asc(workOrderStatuses.createdAt);
          break;
        default:
          orderByClause = asc(workOrderStatuses.position);
      }
    } else {
      orderByClause = asc(workOrderStatuses.position);
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(workOrderStatuses)
      .where(whereClause)
      .then((results) => results[0]);

    const total = totalResult?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Get paginated data
    const workOrderStatusResults = await this.db
      .select({
        id: workOrderStatuses.id,
        statusCode: workOrderStatuses.statusCode,
        statusName: workOrderStatuses.statusName,
        description: workOrderStatuses.description,
        colorCode: workOrderStatuses.colorCode,
        iconName: workOrderStatuses.iconName,
        statusType: workOrderStatuses.statusType,
        isActive: workOrderStatuses.isActive,
        isDefault: workOrderStatuses.isDefault,
        position: workOrderStatuses.position,
      })
      .from(workOrderStatuses)
      .where(whereClause)
      .orderBy(orderByClause)
      .limit(limit)
      .offset(offset);

    const data: WorkOrderStatusListDto[] = workOrderStatusResults.map(
      (status) => ({
        id: status.id,
        statusCode: status.statusCode,
        statusName: status.statusName,
        description: status.description,
        colorCode: status.colorCode,
        iconName: status.iconName,
        statusType: status.statusType,
        isActive: status.isActive,
        isDefault: status.isDefault,
        position: status.position,
      }),
    );

    return {
      data,
      meta: { total, page, totalPages },
    };
  }

  async findOne(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<WorkOrderStatusDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const workOrderStatusResult = await this.db
      .select({
        id: workOrderStatuses.id,
        businessId: workOrderStatuses.businessId,
        statusCode: workOrderStatuses.statusCode,
        statusName: workOrderStatuses.statusName,
        description: workOrderStatuses.description,
        colorCode: workOrderStatuses.colorCode,
        iconName: workOrderStatuses.iconName,
        statusType: workOrderStatuses.statusType,
        isActive: workOrderStatuses.isActive,
        isDefault: workOrderStatuses.isDefault,
        position: workOrderStatuses.position,
        createdBy: workOrderStatuses.createdBy,
        updatedBy: workOrderStatuses.updatedBy,
        createdAt: workOrderStatuses.createdAt,
        updatedAt: workOrderStatuses.updatedAt,
        createdByUser: {
          id: users.id,
          name: users.name,
        },
        updatedByUser: {
          id: users.id,
          name: users.name,
        },
      })
      .from(workOrderStatuses)
      .leftJoin(users, eq(workOrderStatuses.createdBy, users.id))
      .leftJoin(users, eq(workOrderStatuses.updatedBy, users.id))
      .where(
        and(
          eq(workOrderStatuses.id, id),
          eq(workOrderStatuses.businessId, businessId),
          eq(workOrderStatuses.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!workOrderStatusResult) {
      throw new NotFoundException(`Work order status with ID ${id} not found`);
    }

    return {
      id: workOrderStatusResult.id,
      businessId: workOrderStatusResult.businessId,
      statusCode: workOrderStatusResult.statusCode,
      statusName: workOrderStatusResult.statusName,
      description: workOrderStatusResult.description,
      colorCode: workOrderStatusResult.colorCode,
      iconName: workOrderStatusResult.iconName,
      statusType: workOrderStatusResult.statusType,
      isActive: workOrderStatusResult.isActive,
      isDefault: workOrderStatusResult.isDefault,
      position: workOrderStatusResult.position,
      createdBy: workOrderStatusResult.createdByUser?.name || 'Unknown',
      updatedBy: workOrderStatusResult.updatedByUser?.name || null,
      createdAt: workOrderStatusResult.createdAt,
      updatedAt: workOrderStatusResult.updatedAt,
    };
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<WorkOrderStatusSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const workOrderStatusResults = await this.db
      .select({
        id: workOrderStatuses.id,
        statusCode: workOrderStatuses.statusCode,
        statusName: workOrderStatuses.statusName,
        colorCode: workOrderStatuses.colorCode,
        statusType: workOrderStatuses.statusType,
        isActive: workOrderStatuses.isActive,
        isDefault: workOrderStatuses.isDefault,
        position: workOrderStatuses.position,
      })
      .from(workOrderStatuses)
      .where(
        and(
          eq(workOrderStatuses.businessId, businessId),
          eq(workOrderStatuses.isActive, true),
          eq(workOrderStatuses.isDeleted, false),
        ),
      )
      .orderBy(asc(workOrderStatuses.position), asc(workOrderStatuses.id));

    return workOrderStatusResults.map((status) => ({
      id: status.id,
      statusCode: status.statusCode,
      statusName: status.statusName,
      colorCode: status.colorCode,
      statusType: status.statusType,
      isActive: status.isActive,
      isDefault: status.isDefault,
      position: status.position,
    }));
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateWorkOrderStatusDto: UpdateWorkOrderStatusDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if the work order status exists
      const existingWorkOrderStatus = await this.db
        .select()
        .from(workOrderStatuses)
        .where(
          and(
            eq(workOrderStatuses.id, id),
            eq(workOrderStatuses.businessId, businessId),
            eq(workOrderStatuses.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingWorkOrderStatus) {
        throw new NotFoundException(
          `Work order status with ID ${id} not found`,
        );
      }

      // Check for conflicts if status code is being updated
      if (
        updateWorkOrderStatusDto.statusCode &&
        updateWorkOrderStatusDto.statusCode !==
          existingWorkOrderStatus.statusCode
      ) {
        const existingByCode = await this.db
          .select()
          .from(workOrderStatuses)
          .where(
            and(
              eq(workOrderStatuses.businessId, businessId),
              ilike(
                workOrderStatuses.statusCode,
                updateWorkOrderStatusDto.statusCode,
              ),
              eq(workOrderStatuses.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingByCode) {
          throw new ConflictException(
            `Work order status with code "${updateWorkOrderStatusDto.statusCode}" already exists`,
          );
        }
      }

      // Check for conflicts if status name is being updated
      if (
        updateWorkOrderStatusDto.statusName &&
        updateWorkOrderStatusDto.statusName !==
          existingWorkOrderStatus.statusName
      ) {
        const existingByName = await this.db
          .select()
          .from(workOrderStatuses)
          .where(
            and(
              eq(workOrderStatuses.businessId, businessId),
              ilike(
                workOrderStatuses.statusName,
                updateWorkOrderStatusDto.statusName,
              ),
              eq(workOrderStatuses.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingByName) {
          throw new ConflictException(
            `Work order status with name "${updateWorkOrderStatusDto.statusName}" already exists`,
          );
        }
      }

      // Update the work order status
      await this.db
        .update(workOrderStatuses)
        .set({
          ...updateWorkOrderStatusDto,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(workOrderStatuses.id, id));

      // Log the activity
      await this.activityLogService.log(
        ActivityLogName.UPDATE,
        `Work order status "${existingWorkOrderStatus.statusName}" was updated`,
        { id: id, type: 'work-order-status' },
        { id: userId, type: 'user' },
        { workOrderStatusId: id, businessId },
        metadata,
      );

      return {
        id,
        message: 'Work order status updated successfully',
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update work order status: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if the work order status exists
      const existingWorkOrderStatus = await this.db
        .select()
        .from(workOrderStatuses)
        .where(
          and(
            eq(workOrderStatuses.id, id),
            eq(workOrderStatuses.businessId, businessId),
            eq(workOrderStatuses.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingWorkOrderStatus) {
        throw new NotFoundException(
          `Work order status with ID ${id} not found`,
        );
      }

      // Soft delete the work order status
      await this.db
        .update(workOrderStatuses)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(workOrderStatuses.id, id));

      // Log the activity
      await this.activityLogService.log(
        ActivityLogName.DELETE,
        `Work order status "${existingWorkOrderStatus.statusName}" was deleted`,
        { id: id, type: 'work-order-status' },
        { id: userId, type: 'user' },
        { workOrderStatusId: id, businessId },
        metadata,
      );

      return {
        success: true,
        message: `Work order status with ID ${id} has been deleted`,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete work order status: ${error.message}`,
      );
    }
  }

  // Utility method to reorder positions when inserting at a specific position
  private async reorderPositions(
    tx: any,
    businessId: string,
    insertPosition: number,
  ): Promise<void> {
    // Shift all statuses at or after the insert position down by 1
    await tx
      .update(workOrderStatuses)
      .set({
        position: sql`${workOrderStatuses.position} + 1`,
      })
      .where(
        and(
          eq(workOrderStatuses.businessId, businessId),
          gte(workOrderStatuses.position, insertPosition),
          eq(workOrderStatuses.isDeleted, false),
        ),
      );
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createWorkOrderStatusDtos: CreateWorkOrderStatusDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[]; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (
        !createWorkOrderStatusDtos ||
        createWorkOrderStatusDtos.length === 0
      ) {
        throw new BadRequestException('No work order statuses provided');
      }

      // Check for duplicate status codes and names within the batch
      const statusCodes = createWorkOrderStatusDtos.map((dto) =>
        dto.statusCode.toLowerCase(),
      );
      const statusNames = createWorkOrderStatusDtos.map((dto) =>
        dto.statusName.toLowerCase(),
      );

      const duplicateCodes = statusCodes.filter(
        (code, index) => statusCodes.indexOf(code) !== index,
      );
      const duplicateNames = statusNames.filter(
        (name, index) => statusNames.indexOf(name) !== index,
      );

      if (duplicateCodes.length > 0) {
        throw new BadRequestException(
          `Duplicate status codes found in batch: ${duplicateCodes.join(', ')}`,
        );
      }

      if (duplicateNames.length > 0) {
        throw new BadRequestException(
          `Duplicate status names found in batch: ${duplicateNames.join(', ')}`,
        );
      }

      // Check for existing status codes and names in the database
      const existingStatuses = await this.db
        .select({
          statusCode: workOrderStatuses.statusCode,
          statusName: workOrderStatuses.statusName,
        })
        .from(workOrderStatuses)
        .where(
          and(
            eq(workOrderStatuses.businessId, businessId),
            eq(workOrderStatuses.isDeleted, false),
          ),
        );

      const existingCodes = existingStatuses.map((s) =>
        s.statusCode.toLowerCase(),
      );
      const existingNames = existingStatuses.map((s) =>
        s.statusName.toLowerCase(),
      );

      const conflictingCodes = statusCodes.filter((code) =>
        existingCodes.includes(code),
      );
      const conflictingNames = statusNames.filter((name) =>
        existingNames.includes(name),
      );

      if (conflictingCodes.length > 0) {
        throw new ConflictException(
          `Status codes already exist: ${conflictingCodes.join(', ')}`,
        );
      }

      if (conflictingNames.length > 0) {
        throw new ConflictException(
          `Status names already exist: ${conflictingNames.join(', ')}`,
        );
      }

      // Create work order statuses in transaction
      const results = await this.db.transaction(async (tx) => {
        const results = [];

        for (let i = 0; i < createWorkOrderStatusDtos.length; i++) {
          const dto = createWorkOrderStatusDtos[i];

          // Shift existing statuses down to make room at position (i + 1)
          await this.reorderPositions(tx, businessId, i + 1);

          const [status] = await tx
            .insert(workOrderStatuses)
            .values({
              businessId,
              statusCode: dto.statusCode,
              statusName: dto.statusName,
              description: dto.description,
              colorCode: dto.colorCode,
              iconName: dto.iconName,
              statusType: dto.statusType,
              isActive: dto.isActive ?? true,
              isDefault: dto.isDefault ?? false,
              position: i + 1, // Sequential positions starting from 1
              createdBy: userId,
            })
            .returning({ id: workOrderStatuses.id });

          results.push(status);
        }

        return results;
      });

      const ids = results.map((result) => result.id);

      // Log activities for all created statuses
      const activityPromises = createWorkOrderStatusDtos.map((dto, index) =>
        this.activityLogService.log(
          ActivityLogName.CREATE,
          `Work order status "${dto.statusName}" was created (bulk)`,
          { id: ids[index], type: 'work-order-status' },
          { id: userId, type: 'user' },
          { workOrderStatusId: ids[index], businessId },
          metadata,
        ),
      );

      await Promise.all(activityPromises);

      return {
        ids,
        message: `${ids.length} work order statuses created successfully`,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create work order statuses: ${error.message}`,
      );
    }
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    workOrderStatusIds: string[],
    metadata?: ActivityMetadata,
  ): Promise<{
    deleted: number;
    message: string;
    deletedIds: string[];
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!workOrderStatusIds || workOrderStatusIds.length === 0) {
        throw new BadRequestException(
          'No work order status IDs provided for deletion',
        );
      }

      // Get all work order statuses that exist and belong to the business
      const existingWorkOrderStatuses = await this.db
        .select({
          id: workOrderStatuses.id,
          statusName: workOrderStatuses.statusName,
          businessId: workOrderStatuses.businessId,
        })
        .from(workOrderStatuses)
        .where(
          and(
            inArray(workOrderStatuses.id, workOrderStatusIds),
            eq(workOrderStatuses.businessId, businessId),
            eq(workOrderStatuses.isDeleted, false),
          ),
        );

      if (existingWorkOrderStatuses.length === 0) {
        throw new NotFoundException(
          'No valid work order statuses found for deletion',
        );
      }

      const deletedIds: string[] = [];
      const currentTime = new Date();

      // Use transaction to ensure all deletions succeed or fail together
      await this.db.transaction(async (tx) => {
        for (const workOrderStatus of existingWorkOrderStatuses) {
          // Soft delete the work order status
          await tx
            .update(workOrderStatuses)
            .set({
              isDeleted: true,
              updatedBy: userId,
              updatedAt: currentTime,
            })
            .where(eq(workOrderStatuses.id, workOrderStatus.id));

          deletedIds.push(workOrderStatus.id);

          // Log the activity for each deleted work order status
          await this.activityLogService.log(
            ActivityLogName.DELETE,
            `Work order status "${workOrderStatus.statusName}" was deleted (bulk)`,
            { id: workOrderStatus.id, type: 'work-order-status' },
            { id: userId, type: 'user' },
            { workOrderStatusId: workOrderStatus.id, businessId },
            metadata,
          );
        }
      });

      return {
        deleted: deletedIds.length,
        message: `${deletedIds.length} work order statuses deleted successfully`,
        deletedIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete work order statuses: ${error.message}`,
      );
    }
  }

  async bulkUpdateStatus(
    userId: string,
    businessId: string | null,
    workOrderStatusIds: string[],
    isActive: boolean,
    metadata?: ActivityMetadata,
  ): Promise<{ message: string; updatedIds: string[]; updatedCount: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!workOrderStatusIds || workOrderStatusIds.length === 0) {
        throw new BadRequestException('No work order status IDs provided');
      }

      // Get all work order statuses that exist and belong to the business
      const existingWorkOrderStatuses = await this.db
        .select({
          id: workOrderStatuses.id,
          statusName: workOrderStatuses.statusName,
        })
        .from(workOrderStatuses)
        .where(
          and(
            inArray(workOrderStatuses.id, workOrderStatusIds),
            eq(workOrderStatuses.businessId, businessId),
            eq(workOrderStatuses.isDeleted, false),
          ),
        );

      if (existingWorkOrderStatuses.length === 0) {
        throw new NotFoundException(
          'No valid work order statuses found for update',
        );
      }

      const updatedIds: string[] = [];

      // Use transaction to ensure all updates succeed or fail together
      await this.db.transaction(async (tx) => {
        for (const workOrderStatus of existingWorkOrderStatuses) {
          await tx
            .update(workOrderStatuses)
            .set({
              isActive,
              updatedBy: userId,
              updatedAt: new Date(),
            })
            .where(eq(workOrderStatuses.id, workOrderStatus.id));

          updatedIds.push(workOrderStatus.id);

          // Log the activity for each updated work order status
          await this.activityLogService.log(
            ActivityLogName.UPDATE,
            `Work order status "${workOrderStatus.statusName}" active status updated to ${isActive} (bulk)`,
            { id: workOrderStatus.id, type: 'work-order-status' },
            { id: userId, type: 'user' },
            {
              workOrderStatusId: workOrderStatus.id,
              businessId,
              newIsActive: isActive,
            },
            metadata,
          );
        }
      });

      return {
        message: `${updatedIds.length} work order statuses updated successfully`,
        updatedIds,
        updatedCount: updatedIds.length,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk update work order status active status: ${error.message}`,
      );
    }
  }

  async updatePositions(
    userId: string,
    businessId: string | null,
    positionUpdates: WorkOrderStatusPositionDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ message: string; updatedIds: string[] }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!positionUpdates || positionUpdates.length === 0) {
        throw new BadRequestException('No position updates provided');
      }

      // Validate that all positions are unique and positive
      const positions = positionUpdates.map((update) => update.position);
      const uniquePositions = [...new Set(positions)];

      if (positions.length !== uniquePositions.length) {
        throw new BadRequestException('Duplicate positions are not allowed');
      }

      if (positions.some((pos) => pos < 1)) {
        throw new BadRequestException(
          'All positions must be positive integers',
        );
      }

      // Verify all work order statuses exist and belong to the business
      const workOrderStatusIds = positionUpdates.map((update) => update.id);
      const existingWorkOrderStatuses = await this.db
        .select({
          id: workOrderStatuses.id,
          statusName: workOrderStatuses.statusName,
        })
        .from(workOrderStatuses)
        .where(
          and(
            inArray(workOrderStatuses.id, workOrderStatusIds),
            eq(workOrderStatuses.businessId, businessId),
            eq(workOrderStatuses.isDeleted, false),
          ),
        );

      if (existingWorkOrderStatuses.length !== positionUpdates.length) {
        throw new BadRequestException(
          'Some work order statuses do not exist or do not belong to this business',
        );
      }

      const updatedIds: string[] = [];

      // Use transaction to ensure all updates succeed or fail together
      await this.db.transaction(async (tx) => {
        for (const update of positionUpdates) {
          await tx
            .update(workOrderStatuses)
            .set({
              position: update.position,
              updatedBy: userId,
              updatedAt: new Date(),
            })
            .where(
              and(
                eq(workOrderStatuses.id, update.id),
                eq(workOrderStatuses.businessId, businessId),
                eq(workOrderStatuses.isDeleted, false),
              ),
            );

          updatedIds.push(update.id);
        }

        // Log activities for all position updates
        const activityPromises = positionUpdates.map((update) => {
          const workOrderStatus = existingWorkOrderStatuses.find(
            (s) => s.id === update.id,
          );
          return this.activityLogService.log(
            ActivityLogName.UPDATE,
            `Work order status "${workOrderStatus?.statusName}" position updated to ${update.position}`,
            { id: update.id, type: 'work-order-status' },
            { id: userId, type: 'user' },
            {
              workOrderStatusId: update.id,
              businessId,
              newPosition: update.position,
            },
            metadata,
          );
        });

        await Promise.all(activityPromises);
      });

      return {
        message: `${updatedIds.length} work order status positions updated successfully`,
        updatedIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update work order status positions: ${error.message}`,
      );
    }
  }

  async checkStatusNameAvailability(
    businessId: string | null,
    statusName: string,
    excludeId?: string,
  ): Promise<{ available: boolean; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const whereConditions = [
      eq(workOrderStatuses.businessId, businessId),
      ilike(workOrderStatuses.statusName, statusName),
      eq(workOrderStatuses.isDeleted, false),
    ];

    if (excludeId) {
      whereConditions.push(sql`${workOrderStatuses.id} != ${excludeId}`);
    }

    const existingWorkOrderStatus = await this.db
      .select()
      .from(workOrderStatuses)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    const available = !existingWorkOrderStatus;

    return {
      available,
      message: available
        ? 'Work order status name is available'
        : 'Work order status name already exists',
    };
  }
}

import {
  Injectable,
  Inject,
  NotFoundException,
  BadRequestException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { warrantyTemplates } from '../drizzle/schema/warranties.schema';
import { users } from '../drizzle/schema/users.schema';
import { products } from '../drizzle/schema/products.schema';
import { CreateWarrantyTemplateDto } from './dto/create-warranty-template.dto';
import { UpdateWarrantyTemplateDto } from './dto/update-warranty-template.dto';
import { WarrantyTemplateDto } from './dto/warranty-template.dto';
import { WarrantyTemplateListDto } from './dto/warranty-template-list.dto';
import {
  and,
  eq,
  ilike,
  sql,
  gte,
  lte,
  or,
  desc,
  asc,
  inArray,
} from 'drizzle-orm';
import { ProductStatus, WarrantyStatus } from '../shared/types';

// Define the specific warranty statuses supported by the warranty templates schema
type WarrantyTemplateStatus = WarrantyStatus.ACTIVE | WarrantyStatus.INACTIVE;
import { WarrantyTemplateSlimDto } from './dto/warranty-template-slim.dto';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { EntityType, ActivitySource } from '../shared/types/activity.enum';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class WarrantyTemplatesService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createWarrantyTemplateDto: CreateWarrantyTemplateDto,
    metadata?: ActivityMetadata,
  ): Promise<WarrantyTemplateDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a warranty with the same name already exists for this business
      // Using ilike for case-insensitive comparison
      const existingWarranty = await this.db
        .select()
        .from(warrantyTemplates)
        .where(
          and(
            eq(warrantyTemplates.businessId, businessId),
            ilike(
              warrantyTemplates.templateName,
              createWarrantyTemplateDto.templateName,
            ),
            eq(warrantyTemplates.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingWarranty) {
        throw new ConflictException(
          `A warranty with the name '${createWarrantyTemplateDto.templateName}' already exists for this business`,
        );
      }

      const [newWarranty] = await this.db
        .insert(warrantyTemplates)
        .values({
          businessId: businessId,
          templateCode: createWarrantyTemplateDto.templateCode,
          templateName: createWarrantyTemplateDto.templateName,
          warrantyType: createWarrantyTemplateDto.warrantyType,
          duration: createWarrantyTemplateDto.duration,
          coverageType: createWarrantyTemplateDto.coverageType,
          coverageDetails: createWarrantyTemplateDto.coverageDetails,
          termsConditions: createWarrantyTemplateDto.termsConditions,
          isTransferable: createWarrantyTemplateDto.isTransferable || false,
          autoApply: createWarrantyTemplateDto.autoApply || true,
          durationType: createWarrantyTemplateDto.durationType,
          createdBy: userId,
        })
        .returning();

      // Log the activity
      await this.activityLogService.logCreate(
        newWarranty.id,
        EntityType.TEMPLATE,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return await this.mapToWarrantyTemplateDto(newWarranty);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create warranty: ${error.message}`,
      );
    }
  }

  async findAll(
    businessId: string | null,
    page = 1,
    limit = 10,
  ): Promise<{
    data: WarrantyTemplateListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    // Get user's activeBusinessId
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Find all warrantyTemplates for the user's active business with pagination
    const result = await this.db
      .select()
      .from(warrantyTemplates)
      .where(
        and(
          eq(warrantyTemplates.isDeleted, false),
          eq(warrantyTemplates.status, WarrantyStatus.ACTIVE),
          eq(warrantyTemplates.businessId, businessId),
        ),
      )
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(warrantyTemplates)
      .where(
        and(
          eq(warrantyTemplates.isDeleted, false),
          eq(warrantyTemplates.status, WarrantyStatus.ACTIVE),
          eq(warrantyTemplates.businessId, businessId),
        ),
      );

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Get warranty IDs for product count calculation
    const warrantyIds = result.map((warranty) => warranty.id);

    // Get products count for all warrantyTemplates
    const productsCountMap = await this.getProductsCountForWarranties(
      warrantyIds,
      businessId,
    );

    return {
      data: result.map((warranty) => ({
        id: warranty.id,
        name: warranty.templateName,
        description: warranty.coverageDetails,
        duration: warranty.duration,
        durationType: warranty.durationType,
        status: warranty.status,
        productsCount: productsCountMap.get(warranty.id) || 0,
        createdAt: warranty.createdAt,
        updatedAt: warranty.updatedAt,
      })),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllByBusiness(businessId: string): Promise<WarrantyTemplateDto[]> {
    const result = await this.db
      .select()
      .from(warrantyTemplates)
      .where(
        and(
          eq(warrantyTemplates.isDeleted, false),
          eq(warrantyTemplates.status, WarrantyStatus.ACTIVE),
          eq(warrantyTemplates.businessId, businessId),
        ),
      );

    return await Promise.all(
      result.map((warranty) => this.mapToWarrantyTemplateDto(warranty)),
    );
  }

  async findOne(userId: string, id: string): Promise<WarrantyTemplateDto> {
    // Get the warranty
    const warranty = await this.db
      .select()
      .from(warrantyTemplates)
      .where(
        and(
          eq(warrantyTemplates.id, id),
          eq(warrantyTemplates.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!warranty) {
      throw new NotFoundException(`Warranty with ID ${id} not found`);
    }

    // Get user's activeBusinessId to verify permission
    const user = await this.db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (
      !user ||
      !user.activeBusinessId ||
      user.activeBusinessId !== warranty.businessId
    ) {
      throw new UnauthorizedException('Access denied to this warranty');
    }

    return await this.mapToWarrantyTemplateDto(warranty);
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateWarrantyTemplateDto: UpdateWarrantyTemplateDto,
    metadata?: ActivityMetadata,
  ): Promise<WarrantyTemplateDto> {
    // Get the warranty
    const existingWarranty = await this.db
      .select()
      .from(warrantyTemplates)
      .where(
        and(
          eq(warrantyTemplates.id, id),
          eq(warrantyTemplates.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingWarranty) {
      throw new NotFoundException(`Warranty with ID ${id} not found`);
    }

    if (businessId !== existingWarranty.businessId) {
      throw new UnauthorizedException('Access denied to update this warranty');
    }

    // If name is being updated, check if it would conflict with an existing warranty
    if (
      updateWarrantyTemplateDto.templateName &&
      updateWarrantyTemplateDto.templateName !== existingWarranty.templateName
    ) {
      const nameConflict = await this.db
        .select()
        .from(warrantyTemplates)
        .where(
          and(
            eq(warrantyTemplates.businessId, businessId),
            ilike(
              warrantyTemplates.templateName,
              updateWarrantyTemplateDto.templateName,
            ),
            eq(warrantyTemplates.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (nameConflict) {
        throw new ConflictException(
          `A warranty with the name '${updateWarrantyTemplateDto.templateName}' already exists for this business`,
        );
      }
    }

    try {
      const [updatedWarranty] = await this.db
        .update(warrantyTemplates)
        .set({
          ...updateWarrantyTemplateDto,
          ...(updateWarrantyTemplateDto.status && {
            status: updateWarrantyTemplateDto.status as WarrantyTemplateStatus,
          }),
          durationType: updateWarrantyTemplateDto.durationType,
          updatedAt: new Date(),
        })
        .where(eq(warrantyTemplates.id, id))
        .returning();

      // Log the activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.TEMPLATE,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return await this.mapToWarrantyTemplateDto(updatedWarranty);
    } catch (error) {
      throw new BadRequestException(
        `Failed to update warranty: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; message: string }> {
    // Get the warranty
    const existingWarranty = await this.db
      .select()
      .from(warrantyTemplates)
      .where(
        and(
          eq(warrantyTemplates.id, id),
          eq(warrantyTemplates.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingWarranty) {
      throw new NotFoundException(`Warranty with ID ${id} not found`);
    }

    if (businessId !== existingWarranty.businessId) {
      throw new UnauthorizedException('Access denied to delete this warranty');
    }

    // Soft delete the warranty
    await this.db
      .update(warrantyTemplates)
      .set({
        isDeleted: true,
        deletedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(warrantyTemplates.id, id));

    // Log the activity
    await this.activityLogService.logDelete(
      id,
      EntityType.TEMPLATE,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      success: true,
      message: `Warranty with ID ${id} has been deleted`,
    };
  }

  async checkNameAvailability(
    businessId: string | null,
    name: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingWarranty = await this.db
      .select()
      .from(warrantyTemplates)
      .where(
        and(
          eq(warrantyTemplates.businessId, businessId),
          ilike(warrantyTemplates.templateName, name),
          eq(warrantyTemplates.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingWarranty };
  }

  async findAllSlim(
    businessId: string | null,
  ): Promise<WarrantyTemplateSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Find all warrantyTemplates for the user's active business
    const result = await this.db
      .select({
        id: warrantyTemplates.id,
        name: warrantyTemplates.templateName,
      })
      .from(warrantyTemplates)
      .where(
        and(
          eq(warrantyTemplates.businessId, businessId),
          eq(warrantyTemplates.status, WarrantyStatus.ACTIVE),
        ),
      );

    // Log the activity (optional - remove if not needed)
    // await this.activityLogService.logView(...);

    return result;
  }

  private async mapToWarrantyTemplateDto(
    warranty: typeof warrantyTemplates.$inferSelect,
  ): Promise<WarrantyTemplateDto> {
    // Get products count for this warranty
    const productsCount = await this.getProductsCountForWarranty(
      warranty.id,
      warranty.businessId,
    );

    return {
      id: warranty.id,
      businessId: warranty.businessId,
      templateCode: warranty.templateCode,
      templateName: warranty.templateName,
      warrantyType: warranty.warrantyType,
      duration: warranty.duration,
      durationType: warranty.durationType,
      coverageType: warranty.coverageType,
      coverageDetails: warranty.coverageDetails || null,
      termsConditions: warranty.termsConditions || null,
      isTransferable: warranty.isTransferable || false,
      autoApply: warranty.autoApply || false,
      createdBy: warranty.createdBy,
      updatedBy: warranty.updatedBy || null,
      deletedBy: warranty.deletedBy || null,
      deletedAt: warranty.deletedAt || null,
      status: warranty.status,
      createdAt: warranty.createdAt,
      updatedAt: warranty.updatedAt,
      productsCount,
    };
  }

  // New methods that return only IDs and enhanced functionality
  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createWarrantyTemplateDto: CreateWarrantyTemplateDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const result = await this.create(
      userId,
      businessId,
      createWarrantyTemplateDto,
      metadata,
    );
    return { id: result.id };
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createWarrantiesDto: CreateWarrantyTemplateDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!createWarrantiesDto || createWarrantiesDto.length === 0) {
        throw new BadRequestException(
          'No warrantyTemplates provided for creation',
        );
      }

      // Check for duplicate names within the request
      const requestNames = createWarrantiesDto.map((dto) =>
        dto.templateName.toLowerCase(),
      );
      const duplicateNames = requestNames.filter(
        (name, index) => requestNames.indexOf(name) !== index,
      );
      if (duplicateNames.length > 0) {
        throw new BadRequestException(
          `Duplicate warranty names found in request: ${duplicateNames.join(', ')}`,
        );
      }

      // Check if any warrantyTemplates with the same names already exist for this business
      const existingWarranties = await this.db
        .select()
        .from(warrantyTemplates)
        .where(
          and(
            eq(warrantyTemplates.businessId, businessId),
            sql`LOWER(${warrantyTemplates.templateName}) IN (${requestNames.map((name) => `'${name}'`).join(',')})`,
            eq(warrantyTemplates.isDeleted, false),
          ),
        );

      if (existingWarranties.length > 0) {
        const existingNames = existingWarranties.map((w) => w.templateName);
        throw new ConflictException(
          `Warranties with the following names already exist: ${existingNames.join(', ')}`,
        );
      }

      const createdIds: string[] = [];

      // Use a transaction to ensure all warrantyTemplates are created or none are
      await this.db.transaction(async (tx) => {
        for (const createWarrantyTemplateDto of createWarrantiesDto) {
          const [newWarranty] = await tx
            .insert(warrantyTemplates)
            .values({
              businessId: businessId,
              templateCode: createWarrantyTemplateDto.templateCode,
              warrantyType: createWarrantyTemplateDto.warrantyType,
              coverageType: createWarrantyTemplateDto.coverageType,
              templateName: createWarrantyTemplateDto.templateName,
              coverageDetails: createWarrantyTemplateDto.coverageDetails,
              duration: createWarrantyTemplateDto.duration,
              durationType: createWarrantyTemplateDto.durationType,
              createdBy: userId,
            })
            .returning();

          createdIds.push(newWarranty.id.toString());
        }
      });

      return { ids: createdIds };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create warrantyTemplates: ${error.message}`,
      );
    }
  }

  async findAllOptimized(
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    status?: string,
    sort?: string,
  ): Promise<{
    data: WarrantyTemplateListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    // Get user's activeBusinessId
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const conditions = [
      eq(warrantyTemplates.isDeleted, false),
      eq(warrantyTemplates.businessId, businessId),
    ];

    // Add date filters
    if (from) {
      conditions.push(gte(warrantyTemplates.createdAt, new Date(from)));
    }
    if (to) {
      conditions.push(lte(warrantyTemplates.createdAt, new Date(to)));
    }

    // Add name filter
    if (name) {
      conditions.push(ilike(warrantyTemplates.templateName, `%${name}%`));
    }

    // Add status filter
    if (status) {
      const statusArray = status.split(',').map((s) => s.trim());
      if (statusArray.length === 1) {
        conditions.push(
          eq(
            warrantyTemplates.status,
            statusArray[0] as WarrantyTemplateStatus,
          ),
        );
      } else {
        conditions.push(
          or(
            ...statusArray.map((s) =>
              eq(warrantyTemplates.status, s as WarrantyTemplateStatus),
            ),
          ),
        );
      }
    }

    // Build the final where clause
    const whereClause = and(...conditions);

    // Build sort configuration
    let orderBy;
    if (sort) {
      try {
        const sortConfig = JSON.parse(sort);
        if (Array.isArray(sortConfig) && sortConfig.length > 0) {
          const sortField = sortConfig[0];
          if (sortField.id === 'name') {
            orderBy = sortField.desc
              ? desc(warrantyTemplates.templateName)
              : asc(warrantyTemplates.templateName);
          } else if (sortField.id === 'createdAt') {
            orderBy = sortField.desc
              ? desc(warrantyTemplates.createdAt)
              : asc(warrantyTemplates.createdAt);
          } else if (sortField.id === 'updatedAt') {
            orderBy = sortField.desc
              ? desc(warrantyTemplates.updatedAt)
              : asc(warrantyTemplates.updatedAt);
          }
        }
      } catch {
        // Invalid sort configuration, use default
        orderBy = desc(warrantyTemplates.updatedAt);
      }
    } else {
      orderBy = desc(warrantyTemplates.updatedAt);
    }

    // Find all warrantyTemplates for the user's active business with pagination
    const result = await this.db
      .select()
      .from(warrantyTemplates)
      .where(whereClause)
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(warrantyTemplates)
      .where(whereClause);

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Get warranty IDs for product count calculation
    const warrantyIds = result.map((warranty) => warranty.id);

    // Get products count for all warrantyTemplates
    const productsCountMap = await this.getProductsCountForWarranties(
      warrantyIds,
      businessId,
    );

    return {
      data: result.map((warranty) => ({
        id: warranty.id,
        name: warranty.templateName,
        description: warranty.coverageDetails,
        duration: warranty.duration,
        durationType: warranty.durationType,
        status: warranty.status,
        productsCount: productsCountMap.get(warranty.id) || 0,
        createdAt: warranty.createdAt,
        updatedAt: warranty.updatedAt,
      })),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateWarrantyTemplateDto: UpdateWarrantyTemplateDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    // Get the warranty
    const existingWarranty = await this.db
      .select()
      .from(warrantyTemplates)
      .where(
        and(
          eq(warrantyTemplates.id, id),
          eq(warrantyTemplates.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingWarranty) {
      throw new NotFoundException(`Warranty with ID ${id} not found`);
    }

    if (businessId !== existingWarranty.businessId) {
      throw new UnauthorizedException('Access denied to update this warranty');
    }

    // If name is being updated, check if it would conflict with an existing warranty
    if (
      updateWarrantyTemplateDto.templateName &&
      updateWarrantyTemplateDto.templateName !== existingWarranty.templateName
    ) {
      const nameConflict = await this.db
        .select()
        .from(warrantyTemplates)
        .where(
          and(
            eq(warrantyTemplates.businessId, businessId),
            ilike(
              warrantyTemplates.templateName,
              updateWarrantyTemplateDto.templateName,
            ),
            eq(warrantyTemplates.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (nameConflict) {
        throw new ConflictException(
          `A warranty with the name '${updateWarrantyTemplateDto.templateName}' already exists for this business`,
        );
      }
    }

    try {
      await this.db
        .update(warrantyTemplates)
        .set({
          ...updateWarrantyTemplateDto,
          ...(updateWarrantyTemplateDto.status && {
            status: updateWarrantyTemplateDto.status as WarrantyTemplateStatus,
          }),
          durationType: updateWarrantyTemplateDto.durationType,
          updatedAt: new Date(),
        })
        .where(eq(warrantyTemplates.id, id));

      // Log the activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.TEMPLATE,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id: id.toString() };
    } catch (error) {
      throw new BadRequestException(
        `Failed to update warranty: ${error.message}`,
      );
    }
  }

  async removeAndReturnResponse(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; message: string }> {
    return this.remove(userId, businessId, id, metadata);
  }

  private async getProductsCountForWarranty(
    warrantyId: string,
    businessId: string,
  ): Promise<number> {
    try {
      // Count products with this warranty
      const productResult = await this.db
        .select({ count: sql<number>`count(*)` })
        .from(products)
        .where(
          and(
            eq(products.businessId, businessId),
            eq(products.warrantyId, warrantyId),
            eq(products.status, ProductStatus.ACTIVE),
            eq(products.isDeleted, false),
          ),
        );

      const productCount = Number(productResult[0].count);

      return productCount;
    } catch (error) {
      console.warn(
        `Failed to get products count for warranty ${warrantyId}:`,
        error.message,
      );
      return 0;
    }
  }

  async findAutocomplete(
    businessId: string | null,
    query?: string,
  ): Promise<{ id: string; name: string }[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const conditions = [
      eq(warrantyTemplates.businessId, businessId),
      eq(warrantyTemplates.status, WarrantyStatus.ACTIVE),
      eq(warrantyTemplates.isDeleted, false),
    ];

    if (query) {
      conditions.push(ilike(warrantyTemplates.templateName, `%${query}%`));
    }

    const result = await this.db
      .select({
        id: warrantyTemplates.id,
        name: warrantyTemplates.templateName,
      })
      .from(warrantyTemplates)
      .where(and(...conditions))
      .orderBy(asc(warrantyTemplates.templateName))
      .limit(10);

    return result;
  }

  async bulkUpdateStatus(
    userId: string,
    businessId: string | null,
    ids: string[],
    status: WarrantyStatus,
    metadata?: ActivityMetadata,
  ): Promise<{
    updated: number;
    message: string;
    updatedIds: string[];
    failed?: { warrantyId: string; error: string }[];
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!ids || ids.length === 0) {
      throw new BadRequestException('No warranty IDs provided');
    }

    try {
      // Verify all warrantyTemplates exist and belong to the business
      const existingWarranties = await this.db
        .select({ id: warrantyTemplates.id })
        .from(warrantyTemplates)
        .where(
          and(
            eq(warrantyTemplates.businessId, businessId),
            inArray(warrantyTemplates.id, ids),
            eq(warrantyTemplates.isDeleted, false),
          ),
        );

      const existingIds = existingWarranties.map((w) => w.id);
      const notFoundIds = ids.filter((id) => !existingIds.includes(id));

      if (notFoundIds.length > 0) {
        return {
          updated: 0,
          message: 'Some warrantyTemplates not found or access denied',
          updatedIds: [],
          failed: notFoundIds.map((id) => ({
            warrantyId: id,
            error: 'Warranty not found or access denied',
          })),
        };
      }

      // Update all warrantyTemplates
      const updateResult = await this.db
        .update(warrantyTemplates)
        .set({
          status: status as WarrantyTemplateStatus,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(warrantyTemplates.businessId, businessId),
            inArray(warrantyTemplates.id, existingIds),
          ),
        )
        .returning({ id: warrantyTemplates.id });

      // Log activities for all updated warranties
      for (const warranty of updateResult) {
        await this.activityLogService.logUpdate(
          warranty.id,
          EntityType.TEMPLATE,
          userId,
          businessId,
          {
            source: metadata?.source || ActivitySource.WEB,
            ipAddress: metadata?.ipAddress,
            userAgent: metadata?.userAgent,
            sessionId: metadata?.sessionId,
          },
        );
      }

      return {
        updated: updateResult.length,
        message: `Successfully updated ${updateResult.length} warrantyTemplates`,
        updatedIds: updateResult.map((w) => w.id),
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to bulk update warranty status: ${error.message}`,
      );
    }
  }

  async bulkRemove(
    userId: string,
    businessId: string | null,
    ids: string[],
    metadata?: ActivityMetadata,
  ): Promise<{
    deletedCount: number;
    deletedIds: string[];
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!ids || ids.length === 0) {
      throw new BadRequestException('No warranty IDs provided');
    }

    try {
      // Verify all warrantyTemplates exist and belong to the business
      const existingWarranties = await this.db
        .select({ id: warrantyTemplates.id })
        .from(warrantyTemplates)
        .where(
          and(
            eq(warrantyTemplates.businessId, businessId),
            inArray(warrantyTemplates.id, ids),
            eq(warrantyTemplates.isDeleted, false),
          ),
        );

      const existingIds = existingWarranties.map((w) => w.id);

      if (existingIds.length === 0) {
        return {
          deletedCount: 0,
          deletedIds: [],
        };
      }

      // Soft delete all warrantyTemplates
      const deleteResult = await this.db
        .update(warrantyTemplates)
        .set({
          isDeleted: true,
          deletedBy: userId,
          deletedAt: new Date(),
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(warrantyTemplates.businessId, businessId),
            inArray(warrantyTemplates.id, existingIds),
          ),
        )
        .returning({ id: warrantyTemplates.id });

      // Log activities for all deleted warranties
      for (const warranty of deleteResult) {
        await this.activityLogService.logDelete(
          warranty.id,
          EntityType.TEMPLATE,
          userId,
          businessId,
          {
            source: metadata?.source || ActivitySource.WEB,
            ipAddress: metadata?.ipAddress,
            userAgent: metadata?.userAgent,
            sessionId: metadata?.sessionId,
          },
        );
      }

      return {
        deletedCount: deleteResult.length,
        deletedIds: deleteResult.map((w) => w.id),
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to bulk delete warrantyTemplates: ${error.message}`,
      );
    }
  }

  private async getProductsCountForWarranties(
    warrantyIds: string[],
    businessId: string,
  ): Promise<Map<string, number>> {
    try {
      if (warrantyIds.length === 0) {
        return new Map();
      }

      // Count products for each warranty
      const productResults = await this.db
        .select({
          warrantyId: products.warrantyId,
          count: sql<number>`count(*)`.as('count'),
        })
        .from(products)
        .where(
          and(
            eq(products.businessId, businessId),
            inArray(products.warrantyId, warrantyIds),
            eq(products.status, ProductStatus.ACTIVE),
            eq(products.isDeleted, false),
          ),
        )
        .groupBy(products.warrantyId);

      // Combine the counts
      const countsMap = new Map<string, number>();

      // Initialize all warranty IDs with 0
      warrantyIds.forEach((id) => countsMap.set(id, 0));

      // Add product counts
      productResults.forEach((result) => {
        if (result.warrantyId) {
          countsMap.set(
            result.warrantyId,
            (countsMap.get(result.warrantyId) || 0) + result.count,
          );
        }
      });

      return countsMap;
    } catch (error) {
      console.warn(
        'Failed to get products count for warrantyTemplates:',
        error.message,
      );
      return new Map();
    }
  }
}

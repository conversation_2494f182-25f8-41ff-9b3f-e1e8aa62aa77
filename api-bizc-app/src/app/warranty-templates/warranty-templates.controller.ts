import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
} from '@nestjs/common';
import { WarrantyTemplatesService } from './warranty-templates.service';
import { CreateWarrantyTemplateDto } from './dto/create-warranty-template.dto';
import { BulkCreateWarrantyTemplateDto } from './dto/bulk-create-warranty-template.dto';
import { UpdateWarrantyTemplateDto } from './dto/update-warranty-template.dto';
import { WarrantyTemplateDto } from './dto/warranty-template.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { WarrantyTemplateNameAvailabilityResponseDto } from './dto/check-warranty-template-name.dto';
import { WarrantyTemplateSlimDto } from './dto/warranty-template-slim.dto';
import { PaginatedWarrantyTemplatesResponseDto } from './dto/paginated-warranty-templates-response.dto';
import { DeleteWarrantyTemplateResponseDto } from './dto/delete-warranty-template-response.dto';
import { WarrantyTemplateIdResponseDto } from './dto/warranty-template-id-response.dto';
import { BulkWarrantyTemplateIdsResponseDto } from './dto/bulk-warranty-template-ids-response.dto';
import { WarrantyTemplateAutocompleteDto } from './dto/warranty-template-autocomplete.dto';
import { BulkUpdateWarrantyTemplateStatusDto } from './dto/bulk-update-warranty-template-status.dto';
import { BulkUpdateWarrantyTemplateStatusResponseDto } from './dto/bulk-update-warranty-template-status-response.dto';
import { BulkDeleteWarrantyTemplateDto } from './dto/bulk-delete-warranty-template.dto';
import { BulkDeleteWarrantyTemplateResponseDto } from './dto/bulk-delete-warranty-template-response.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import { Permission } from '../shared/types/permission.enum';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

@ApiTags('warranty-templates')
@Controller('warranty-templates')
@UseGuards(PermissionsGuard)
export class WarrantyTemplatesController {
  constructor(
    private readonly warrantyTemplatesService: WarrantyTemplatesService,
  ) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.WARRANTY_CREATE)
  @ApiOperation({ summary: 'Create a new warranty' })
  @ApiResponse({
    status: 201,
    description: 'The warranty has been successfully created',
    type: WarrantyTemplateIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  create(
    @Request() req: any,
    @Body() createWarrantyTemplateDto: CreateWarrantyTemplateDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<WarrantyTemplateIdResponseDto> {
    return this.warrantyTemplatesService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createWarrantyTemplateDto,
      metadata,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WARRANTY_CREATE)
  @ApiOperation({ summary: 'Bulk create warranties' })
  @ApiResponse({
    status: 201,
    description: 'The warranties have been successfully created',
    type: BulkWarrantyTemplateIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or duplicate names',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Warranty names already exist',
  })
  bulkCreate(
    @Request() req: any,
    @Body() bulkCreateWarrantyTemplateDto: BulkCreateWarrantyTemplateDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkWarrantyTemplateIdsResponseDto> {
    return this.warrantyTemplatesService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateWarrantyTemplateDto.warranties,
      metadata,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.WARRANTY_READ)
  @ApiOperation({ summary: 'Get all warranties for the active business' })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Filter from date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-05-24',
  })
  @ApiQuery({
    name: 'to',
    description: 'Filter to date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-05-24',
  })
  @ApiQuery({
    name: 'name',
    description: 'Filter by warranty name',
    required: false,
    type: String,
    example: '1 Year Warranty',
  })
  @ApiQuery({
    name: 'status',
    description:
      'Filter by status (comma-separated for multiple values). Supports URL encoding: status=active%2Cinactive',
    required: false,
    type: String,
    example: 'active,inactive',
  })
  @ApiQuery({
    name: 'filters',
    description:
      'Advanced filters as JSON string with operator support. Supported operators: iLike (contains), notILike (does not contain), eq (is), ne (is not), isEmpty (is empty), isNotEmpty (is not empty)',
    required: false,
    type: String,
    example:
      '[{"id":"name","value":"Warranty","operator":"iLike","type":"text","rowId":"1"},{"id":"status","value":"active","operator":"eq","type":"select","rowId":"2"}]',
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for advanced filters',
    required: false,
    type: String,
    enum: ['and', 'or'],
    example: 'and',
  })
  @ApiQuery({
    name: 'sort',
    description:
      'Sort configuration as JSON string. Supported fields: name, createdAt, updatedAt',
    required: false,
    type: String,
    example: '[{"id":"updatedAt","desc":true}]',
  })
  @ApiResponse({
    status: 200,
    description:
      "Returns all active warranties for the user's active business with pagination",
    type: PaginatedWarrantyTemplatesResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req: any,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('name') name?: string,
    @Query('status') status?: string,
    @Query('sort') sort?: string,
  ): Promise<PaginatedWarrantyTemplatesResponseDto> {
    return this.warrantyTemplatesService.findAllOptimized(
      req.user.activeBusinessId,
      page ? parseInt(page.toString()) : undefined,
      limit ? parseInt(limit.toString()) : undefined,
      from,
      to,
      name,
      status,
      sort,
    );
  }

  @Get('check-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WARRANTY_READ)
  @ApiOperation({ summary: 'Check if a warranty name is available' })
  @ApiQuery({
    name: 'name',
    description: 'Warranty name to check',
    required: true,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns whether the warranty name is available',
    type: WarrantyTemplateNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkNameAvailability(
    @Request() req: any,
    @Query('name') name: string,
  ): Promise<WarrantyTemplateNameAvailabilityResponseDto> {
    return this.warrantyTemplatesService.checkNameAvailability(
      req.user.activeBusinessId,
      name,
    );
  }

  @Get('autocomplete')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WARRANTY_READ)
  @ApiOperation({ summary: 'Get warranties for autocomplete' })
  @ApiQuery({
    name: 'query',
    description: 'Search query for warranty names',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns warranties for autocomplete',
    type: [WarrantyTemplateAutocompleteDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAutocomplete(
    @Request() req: any,
    @Query('query') query?: string,
  ): Promise<WarrantyTemplateAutocompleteDto[]> {
    return this.warrantyTemplatesService.findAutocomplete(
      req.user.activeBusinessId,
      query,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WARRANTY_READ)
  @ApiOperation({ summary: 'Get all warranties with only id and name fields' })
  @ApiResponse({
    status: 200,
    description: 'Returns all warranties with only id and name',
    type: [WarrantyTemplateSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req: any): Promise<WarrantyTemplateSlimDto[]> {
    return this.warrantyTemplatesService.findAllSlim(req.user.activeBusinessId);
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WARRANTY_READ)
  @ApiOperation({ summary: 'Get a warranty by ID' })
  @ApiParam({
    name: 'id',
    description: 'Warranty ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the warranty',
    type: WarrantyTemplateDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Warranty not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this warranty',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findOne(
    @Request() req: any,
    @Param('id') id: string,
  ): Promise<WarrantyTemplateDto> {
    return this.warrantyTemplatesService.findOne(req.user.id, id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WARRANTY_UPDATE)
  @ApiOperation({ summary: 'Update a warranty' })
  @ApiParam({
    name: 'id',
    description: 'Warranty ID',
  })
  @ApiResponse({
    status: 200,
    description: 'The warranty has been successfully updated',
    type: WarrantyTemplateIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 404,
    description: 'Warranty not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to update this warranty',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  update(
    @Request() req: any,
    @Param('id') id: string,
    @Body() updateWarrantyTemplateDto: UpdateWarrantyTemplateDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<WarrantyTemplateIdResponseDto> {
    return this.warrantyTemplatesService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateWarrantyTemplateDto,
      metadata,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WARRANTY_DELETE)
  @ApiOperation({ summary: 'Delete a warranty' })
  @ApiParam({
    name: 'id',
    description: 'Warranty ID',
  })
  @ApiResponse({
    status: 200,
    description: 'The warranty has been successfully deleted',
    type: DeleteWarrantyTemplateResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Warranty not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to delete this warranty',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  remove(
    @Request() req: any,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteWarrantyTemplateResponseDto> {
    return this.warrantyTemplatesService.removeAndReturnResponse(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }

  @Patch('bulk/status')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WARRANTY_UPDATE)
  @ApiOperation({ summary: 'Bulk update warranty status' })
  @ApiResponse({
    status: 200,
    description: 'The warranties status has been successfully updated',
    type: BulkUpdateWarrantyTemplateStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or no warranty IDs provided',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  bulkUpdateStatus(
    @Request() req: any,
    @Body() bulkUpdateWarrantyStatusDto: BulkUpdateWarrantyTemplateStatusDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkUpdateWarrantyTemplateStatusResponseDto> {
    return this.warrantyTemplatesService.bulkUpdateStatus(
      req.user.id,
      req.user.activeBusinessId,
      bulkUpdateWarrantyStatusDto.ids,
      bulkUpdateWarrantyStatusDto.status,
      metadata,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WARRANTY_DELETE)
  @ApiOperation({ summary: 'Bulk delete warranties' })
  @ApiResponse({
    status: 200,
    description: 'The warranties have been successfully deleted',
    type: BulkDeleteWarrantyTemplateResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - No warranty IDs provided',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  bulkRemove(
    @Request() req: any,
    @Body() bulkDeleteWarrantyTemplateDto: BulkDeleteWarrantyTemplateDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteWarrantyTemplateResponseDto> {
    return this.warrantyTemplatesService.bulkRemove(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteWarrantyTemplateDto.ids,
      metadata,
    );
  }
}
